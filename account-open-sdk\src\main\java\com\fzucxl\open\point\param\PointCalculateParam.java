package com.fzucxl.open.point.param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 计算积分请求参数
 */
public class PointCalculateParam {
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    @Size(max = 64, message = "用户ID长度不能超过64")
    private String userId;
    
    /**
     * 账户代码
     */
    @NotBlank(message = "账户代码不能为空")
    @Size(max = 32, message = "账户代码长度不能超过32")
    private String accountCode;
    
    /**
     * 业务场景代码
     */
    @NotBlank(message = "业务场景代码不能为空")
    @Size(max = 32, message = "业务场景代码长度不能超过32")
    private String scene;
    
    /**
     * 业务数据
     */
    @NotNull(message = "业务数据不能为空")
    private Object bizData;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public Object getBizData() {
        return bizData;
    }

    public void setBizData(Object bizData) {
        this.bizData = bizData;
    }
}