applicationType: default
defaultPackage: com.fzucxl
testFramework: junit
sourceLanguage: java
buildTool: maven
features: [annotation-api, app-name, cache-caffeine, data, data-jdbc, discovery-client, discovery-core, h2, http-client, java, java-application, jdbc-hikari, junit, kafka, logback, maven, maven-enforcer-plugin, micronaut-aot, micronaut-http-validation, netty-server, openapi, openapi-adoc, openapi-explorer, readme, security-annotations, security-jwt, serialization-jackson, shade, static-resources, swagger-ui, test-resources, validation, yaml, yaml-build]
