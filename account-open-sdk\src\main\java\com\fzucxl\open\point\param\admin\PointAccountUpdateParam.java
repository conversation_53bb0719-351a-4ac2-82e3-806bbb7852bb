package com.fzucxl.open.point.param.admin;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 更新积分账户请求参数
 */
public class PointAccountUpdateParam {
    
    /**
     * 账户代码
     */
    @NotBlank(message = "账户代码不能为空")
    @Size(max = 32, message = "账户代码长度不能超过32")
    private String accountCode;
    
    /**
     * 账户名称
     */
    @Size(max = 64, message = "账户名称长度不能超过64")
    private String accountName;
    
    /**
     * 账户类型
     */
    private String accountType;
    
    /**
     * 品牌编码
     */
    @Size(max = 32, message = "品牌编码长度不能超过32")
    private String brandCode;
    
    /**
     * 状态：ACTIVE-激活, INACTIVE-停用
     */
    private String status;
    
    /**
     * 账户描述
     */
    @Size(max = 255, message = "账户描述长度不能超过255")
    private String description;
    
    /**
     * 基础配置
     */
    private String basicConfig;
    
    /**
     * 风控配置
     */
    private String riskControlConfig;
    
    /**
     * 扩展配置
     */
    private String extensionConfig;

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBasicConfig() {
        return basicConfig;
    }

    public void setBasicConfig(String basicConfig) {
        this.basicConfig = basicConfig;
    }

    public String getRiskControlConfig() {
        return riskControlConfig;
    }

    public void setRiskControlConfig(String riskControlConfig) {
        this.riskControlConfig = riskControlConfig;
    }

    public String getExtensionConfig() {
        return extensionConfig;
    }

    public void setExtensionConfig(String extensionConfig) {
        this.extensionConfig = extensionConfig;
    }
}