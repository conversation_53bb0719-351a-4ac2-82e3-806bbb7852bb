package com.fzucxl.open.point.param.admin;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 查询积分规则列表请求参数
 */
public class PointRuleListQueryParam {
    
    /**
     * 账户代码
     */
    @NotBlank(message = "账户代码不能为空")
    @Size(max = 32, message = "账户代码长度不能超过32")
    private String accountCode;
    
    /**
     * 规则代码
     */
    @Size(max = 64, message = "规则代码长度不能超过64")
    private String ruleCode;
    
    /**
     * 规则名称，支持模糊查询
     */
    @Size(max = 100, message = "规则名称长度不能超过100")
    private String ruleName;
    
    /**
     * 规则类型
     */
    @Size(max = 32, message = "规则类型长度不能超过32")
    private String ruleType;
    
    /**
     * 状态：ACTIVE-生效，INACTIVE-未生效
     */
    @Size(max = 20, message = "状态长度不能超过20")
    private String status;
    
    /**
     * 页码，默认1
     */
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum = 1;
    
    /**
     * 每页记录数，默认10
     */
    @Min(value = 1, message = "每页记录数最小为1")
    @Max(value = 100, message = "每页记录数最大为100")
    private Integer pageSize = 10;

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
