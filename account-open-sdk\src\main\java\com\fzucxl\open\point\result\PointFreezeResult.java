package com.fzucxl.open.point.result;

/**
 * 冻结积分响应结果
 */
public class PointFreezeResult {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 冻结记录ID
     */
    private String freezeId;
    
    /**
     * 系统生成的交易ID
     */
    private String transactionId;
    
    /**
     * 实际冻结的积分数量
     */
    private Long point;
    
    /**
     * 冻结前可用余额
     */
    private Long beforeAvailable;
    
    /**
     * 冻结后可用余额
     */
    private Long afterAvailable;
    
    /**
     * 冻结前冻结余额
     */
    private Long beforeFrozen;
    
    /**
     * 冻结后冻结余额
     */
    private Long afterFrozen;
    
    /**
     * 冻结过期时间
     */
    private String expireTime;
    
    /**
     * 处理状态
     */
    private String status;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getFreezeId() {
        return freezeId;
    }

    public void setFreezeId(String freezeId) {
        this.freezeId = freezeId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Long getPoint() {
        return point;
    }

    public void setPoint(Long point) {
        this.point = point;
    }

    public Long getBeforeAvailable() {
        return beforeAvailable;
    }

    public void setBeforeAvailable(Long beforeAvailable) {
        this.beforeAvailable = beforeAvailable;
    }

    public Long getAfterAvailable() {
        return afterAvailable;
    }

    public void setAfterAvailable(Long afterAvailable) {
        this.afterAvailable = afterAvailable;
    }

    public Long getBeforeFrozen() {
        return beforeFrozen;
    }

    public void setBeforeFrozen(Long beforeFrozen) {
        this.beforeFrozen = beforeFrozen;
    }

    public Long getAfterFrozen() {
        return afterFrozen;
    }

    public void setAfterFrozen(Long afterFrozen) {
        this.afterFrozen = afterFrozen;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}