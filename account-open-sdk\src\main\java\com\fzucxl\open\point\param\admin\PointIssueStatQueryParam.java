package com.fzucxl.open.point.param.admin;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 积分发放统计查询请求参数
 */
public class PointIssueStatQueryParam {
    
    /**
     * 账户代码
     */
    @NotBlank(message = "账户代码不能为空")
    @Size(max = 32, message = "账户代码长度不能超过32")
    private String accountCode;
    
    /**
     * 开始日期，格式：yyyy-MM-dd
     */
    @NotBlank(message = "开始日期不能为空")
    private String startDate;
    
    /**
     * 结束日期，格式：yyyy-MM-dd
     */
    @NotBlank(message = "结束日期不能为空")
    private String endDate;
    
    /**
     * 分组方式：DAY, WEEK, MONTH，默认DAY
     */
    @Size(max = 10, message = "分组方式长度不能超过10")
    private String groupBy = "DAY";
    
    /**
     * 积分来源
     */
    @Size(max = 32, message = "积分来源长度不能超过32")
    private String source;

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getGroupBy() {
        return groupBy;
    }

    public void setGroupBy(String groupBy) {
        this.groupBy = groupBy;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
}
