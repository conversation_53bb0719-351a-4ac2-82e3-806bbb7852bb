package com.fzucxl.open.point.result.admin;

import java.util.List;

/**
 * 积分消费统计响应结果
 */
public class PointConsumeStatResult {
    
    /**
     * 总积分消费量
     */
    private Integer total;
    
    /**
     * 涉及用户数
     */
    private Integer userCount;
    
    /**
     * 统计明细
     */
    private List<StatDetail> detail;
    
    /**
     * 来源分布
     */
    private List<SourceDistribution> sourceDistribution;
    
    /**
     * 统计明细
     */
    public static class StatDetail {
        
        /**
         * 日期
         */
        private String date;
        
        /**
         * 积分数量
         */
        private Integer point;
        
        /**
         * 用户数量
         */
        private Integer userCount;

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public Integer getPoint() {
            return point;
        }

        public void setPoint(Integer point) {
            this.point = point;
        }

        public Integer getUserCount() {
            return userCount;
        }

        public void setUserCount(Integer userCount) {
            this.userCount = userCount;
        }
    }
    
    /**
     * 来源分布
     */
    public static class SourceDistribution {
        
        /**
         * 来源
         */
        private String source;
        
        /**
         * 积分数量
         */
        private Integer point;
        
        /**
         * 百分比
         */
        private Double percentage;

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public Integer getPoint() {
            return point;
        }

        public void setPoint(Integer point) {
            this.point = point;
        }

        public Double getPercentage() {
            return percentage;
        }

        public void setPercentage(Double percentage) {
            this.percentage = percentage;
        }
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getUserCount() {
        return userCount;
    }

    public void setUserCount(Integer userCount) {
        this.userCount = userCount;
    }

    public List<StatDetail> getDetail() {
        return detail;
    }

    public void setDetail(List<StatDetail> detail) {
        this.detail = detail;
    }

    public List<SourceDistribution> getSourceDistribution() {
        return sourceDistribution;
    }

    public void setSourceDistribution(List<SourceDistribution> sourceDistribution) {
        this.sourceDistribution = sourceDistribution;
    }
}