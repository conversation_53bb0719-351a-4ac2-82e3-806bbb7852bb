# AOT configuration properties for jar packaging
# Please review carefully the optimizations enabled below
# Check https://micronaut-projects.github.io/micronaut-aot/latest/guide/ for more details
# Caches environment property values: environment properties will be deemed immutable after application startup.
cached.environment.enabled=true
# Precomputes Micronaut configuration property keys from the current environment variables
precompute.environment.properties.enabled=true
# Replaces logback.xml with a pure Java configuration
logback.xml.to.java.enabled=true
# Converts YAML configuration files to Java configuration
yaml.to.java.config.enabled=true
# Scans for service types ahead-of-time, avoiding classpath scanning at startup
serviceloading.jit.enabled=true
# Scans reactive types at build time instead of runtime
scan.reactive.types.enabled=true
# Deduces the environment at build time instead of runtime
deduce.environment.enabled=true
# Checks for the existence of some types at build time instead of runtime
known.missing.types.enabled=true
# Precomputes property sources at build time
sealed.property.source.enabled=true
# The list of service types to be scanned (comma separated)
service.types=io.micronaut.context.env.PropertySourceLoader,io.micronaut.inject.BeanConfiguration,io.micronaut.inject.BeanDefinitionReference,io.micronaut.http.HttpRequestFactory,io.micronaut.http.HttpResponseFactory,io.micronaut.core.beans.BeanIntrospectionReference,io.micronaut.core.convert.TypeConverterRegistrar,io.micronaut.context.env.PropertyExpressionResolver
# A list of types that the AOT analyzer needs to check for existence (comma separated)
known.missing.types.list=io.reactivex.Observable,reactor.core.publisher.Flux,kotlinx.coroutines.flow.Flow,io.reactivex.rxjava3.core.Flowable,io.reactivex.rxjava3.core.Observable,io.reactivex.Single,reactor.core.publisher.Mono,io.reactivex.Maybe,io.reactivex.rxjava3.core.Single,io.reactivex.rxjava3.core.Maybe,io.reactivex.Completable,io.reactivex.rxjava3.core.Completable,io.methvin.watchservice.MacOSXListeningWatchService,io.micronaut.core.async.publisher.CompletableFuturePublisher,io.micronaut.core.async.publisher.Publishers.JustPublisher,io.micronaut.core.async.subscriber.Completable

