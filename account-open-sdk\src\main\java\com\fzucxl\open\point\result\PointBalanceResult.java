package com.fzucxl.open.point.result;

/**
 * 查询用户积分余额响应结果
 */
public class PointBalanceResult {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 账户代码
     */
    private String accountCode;
    
    /**
     * 总积分（历史累计）
     */
    private Long totalPoint;
    
    /**
     * 可用积分
     */
    private Long availablePoint;
    
    /**
     * 冻结积分
     */
    private Long frozenPoint;
    
    /**
     * 已过期积分
     */
    private Long expiredPoint;
    
    /**
     * 30天内即将过期积分
     */
    private Long expiringSoonPoint;
    
    /**
     * 下次过期时间
     */
    private String nextExpireDate;
    
    /**
     * 最后更新时间
     */
    private String lastUpdateTime;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public Long getTotalPoint() {
        return totalPoint;
    }

    public void setTotalPoint(Long totalPoint) {
        this.totalPoint = totalPoint;
    }

    public Long getAvailablePoint() {
        return availablePoint;
    }

    public void setAvailablePoint(Long availablePoint) {
        this.availablePoint = availablePoint;
    }

    public Long getFrozenPoint() {
        return frozenPoint;
    }

    public void setFrozenPoint(Long frozenPoint) {
        this.frozenPoint = frozenPoint;
    }

    public Long getExpiredPoint() {
        return expiredPoint;
    }

    public void setExpiredPoint(Long expiredPoint) {
        this.expiredPoint = expiredPoint;
    }

    public Long getExpiringSoonPoint() {
        return expiringSoonPoint;
    }

    public void setExpiringSoonPoint(Long expiringSoonPoint) {
        this.expiringSoonPoint = expiringSoonPoint;
    }

    public String getNextExpireDate() {
        return nextExpireDate;
    }

    public void setNextExpireDate(String nextExpireDate) {
        this.nextExpireDate = nextExpireDate;
    }

    public String getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(String lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}