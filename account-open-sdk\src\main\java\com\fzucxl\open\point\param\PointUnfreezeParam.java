package com.fzucxl.open.point.param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;

/**
 * 解冻积分请求参数
 */
public class PointUnfreezeParam {
    
    /**
     * 用户唯一标识
     */
    @NotBlank(message = "用户ID不能为空")
    @Size(max = 64, message = "用户ID长度不能超过64")
    private String userId;
    
    /**
     * 账户代码
     */
    @NotBlank(message = "账户代码不能为空")
    @Size(max = 32, message = "账户代码长度不能超过32")
    private String accountCode;
    
    /**
     * 冻结记录ID
     */
    @NotBlank(message = "冻结记录ID不能为空")
    @Size(max = 64, message = "冻结记录ID长度不能超过64")
    private String freezeId;
    
    /**
     * 解冻积分数量
     */
    @Min(value = 1, message = "解冻积分数量最小为1")
    private Long point;
    
    /**
     * 解冻原因
     */
    @NotBlank(message = "解冻原因不能为空")
    @Size(max = 100, message = "解冻原因长度不能超过100")
    private String reason;
    
    /**
     * 业务单号
     */
    @NotBlank(message = "业务单号不能为空")
    @Size(max = 64, message = "业务单号长度不能超过64")
    private String businessId;
    
    /**
     * 交易ID
     */
    @Size(max = 64, message = "交易ID长度不能超过64")
    private String transactionId;
    
    /**
     * 备注信息
     */
    @Size(max = 100, message = "备注信息长度不能超过100")
    private String remark;
    
    /**
     * 回调通知地址
     */
    private String notifyUrl;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getFreezeId() {
        return freezeId;
    }

    public void setFreezeId(String freezeId) {
        this.freezeId = freezeId;
    }

    public Long getPoint() {
        return point;
    }

    public void setPoint(Long point) {
        this.point = point;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }
}