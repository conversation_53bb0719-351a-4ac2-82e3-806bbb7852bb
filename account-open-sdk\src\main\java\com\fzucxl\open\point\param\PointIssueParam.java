package com.fzucxl.open.point.param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 发放积分请求参数
 */
public class PointIssueParam {
    
    /**
     * 用户唯一标识
     */
    @NotBlank(message = "用户ID不能为空")
    @Size(max = 64, message = "用户ID长度不能超过64")
    private String userId;
    
    /**
     * 账户代码
     */
    @NotBlank(message = "账户代码不能为空")
    @Size(max = 32, message = "账户代码长度不能超过32")
    private String accountCode;
    
    /**
     * 积分数量
     */
    @NotNull(message = "积分数量不能为空")
    @Min(value = 1, message = "积分数量最小为1")
    @Max(value = 100000, message = "积分数量最大为100000")
    private Long point;
    
    /**
     * 积分来源代码
     */
    @NotBlank(message = "积分来源代码不能为空")
    @Size(max = 32, message = "积分来源代码长度不能超过32")
    private String source;
    
    /**
     * 来源名称
     */
    @Size(max = 64, message = "来源名称长度不能超过64")
    private String sourceName;
    
    /**
     * 详细描述
     */
    @Size(max = 200, message = "详细描述长度不能超过200")
    private String description;
    
    /**
     * 有效期天数
     */
    @Min(value = 1, message = "有效期天数最小为1")
    @Max(value = 3650, message = "有效期天数最大为3650")
    private Integer expireDays = 365;
    
    /**
     * 生效时间
     */
    private String effectiveTime;
    
    /**
     * 业务单号
     */
    @NotBlank(message = "业务单号不能为空")
    @Size(max = 64, message = "业务单号长度不能超过64")
    private String businessId;
    
    /**
     * 交易ID
     */
    @Size(max = 64, message = "交易ID长度不能超过64")
    private String transactionId;
    
    /**
     * 备注信息
     */
    @Size(max = 100, message = "备注信息长度不能超过100")
    private String remark;
    
    /**
     * 回调通知地址
     */
    private String notifyUrl;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public Long getPoint() {
        return point;
    }

    public void setPoint(Long point) {
        this.point = point;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getExpireDays() {
        return expireDays;
    }

    public void setExpireDays(Integer expireDays) {
        this.expireDays = expireDays;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }
}