package com.fzucxl.open.point.param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 查询用户积分余额请求参数
 */
public class PointBalanceQueryParam {
    
    /**
     * 用户唯一标识
     */
    @NotBlank(message = "用户ID不能为空")
    @Size(max = 64, message = "用户ID长度不能超过64")
    private String userId;
    
    /**
     * 账户代码
     */
    @NotBlank(message = "账户代码不能为空")
    @Size(max = 32, message = "账户代码长度不能超过32")
    private String accountCode;
    
    /**
     * 交易ID，用于关联上下游交易
     */
    @Size(max = 64, message = "交易ID长度不能超过64")
    private String transactionId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
}