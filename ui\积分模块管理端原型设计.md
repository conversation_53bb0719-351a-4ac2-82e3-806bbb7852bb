# 积分模块管理端原型设计

## 1. 设计概述

### 1.1 设计说明
本文档定义了积分模块管理端的Web原型设计，包含积分账户管理、配置管理、规则管理等核心功能模块的页面设计。

### 1.2 设计原则
- **简洁易用**：界面简洁，操作流程清晰
- **信息层次**：重要信息突出显示，次要信息适当弱化
- **响应式设计**：支持不同屏幕尺寸的设备访问
- **一致性**：保持整体设计风格和交互模式的一致性

### 1.3 技术规范
- **设计工具**：基于Ant Design组件库
- **布局方式**：左侧导航 + 右侧内容区域
- **色彩规范**：主色调#1890ff，成功色#52c41a，警告色#faad14，错误色#f5222d
- **字体规范**：中文字体14px，英文字体12px

## 2. 系统导航结构

```
积分管理系统
├── 积分账户管理
│   ├── 账户列表
│   └── 账户详情
├── 系统配置
│   ├── 账户配置
│   ├── 属性元数据配置
│   └── 积分规则配置
├── 数据统计
│   ├── 积分发放统计
│   ├── 积分消费统计
│   └── 用户积分排行
└── 系统监控
    ├── 交易监控
    └── 系统日志
```

## 3. 页面原型设计

### 3.1 账户列表查询页面（多账户体系）

#### 3.1.1 积分账户列表页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [积分账户管理] │                                                            │
│  ├ 账户列表    │  积分账户列表                                              │
│  └ 账户详情    │                                                            │
│ [系统配置]     │  ┌─────────────────────────────────────────────────────┐   │
│  ├ 账户配置    │  │ [积分账户] [成长值账户] [等级账户] [勋章账户]        │   │
│  ├ 属性配置    │  └─────────────────────────────────────────────────────┘   │
│  └ 规则配置    │                                                            │
│ [数据统计]     │  ┌─────────────────────────────────────────────────────┐   │
│ [系统监控]     │  │ 搜索条件                                            │   │
│                │  │ 用户ID: [_____________] 账户名称: [__________]      │   │
│                │  │ 状态: [全部▼] 时间范围: [2024-01-01] ~ [2024-01-31] │   │
│                │  │                              [查询] [重置] [导出]   │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 数据表格                                 共123条记录 │   │
│                │  │ ┌──┬────────┬──────┬────────┬────────┬────────┬────┐ │   │
│                │  │ │☐ │用户ID   │账户名称   │总积分   │可用积分 │冻结积分 │操作│ │   │
│                │  │ ├──┼────────┼──────┼────────┼────────┼────────┼────┤ │   │
│                │  │ │☐ │user_001│MALL  │5,000   │4,800   │200     │详情│ │   │
│                │  │ │☐ │user_002│MALL  │3,200   │3,200   │0       │详情│ │   │
│                │  │ │☐ │user_003│APP   │1,500   │1,200   │300     │详情│ │   │
│                │  │ │☐ │user_004│GAME  │8,500   │8,000   │500     │详情│ │   │
│                │  │ │☐ │user_005│MALL  │2,100   │2,100   │0       │详情│ │   │
│                │  │ └──┴────────┴──────┴────────┴────────┴────────┴────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 2 3 4 5 [>]  每页20条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.1.2 成长值账户列表页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [积分账户管理] │                                                            │
│  ├ 账户列表    │  成长值账户列表                                            │
│  └ 账户详情    │                                                            │
│ [系统配置]     │  ┌─────────────────────────────────────────────────────┐   │
│  ├ 账户配置    │  │ [积分账户] [成长值账户] [等级账户] [勋章账户]        │   │
│  ├ 属性配置    │  └─────────────────────────────────────────────────────┘   │
│  └ 规则配置    │                                                            │
│ [数据统计]     │  ┌─────────────────────────────────────────────────────┐   │
│ [系统监控]     │  │ 搜索条件                                            │   │
│                │  │ 用户ID: [_____________] 账户名称: [__________]      │   │
│                │  │ 状态: [全部▼] 时间范围: [2024-01-01] ~ [2024-01-31] │   │
│                │  │                              [查询] [重置] [导出]   │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 数据表格                                 共89条记录  │   │
│                │  │ ┌──┬────────┬──────┬────────┬────────┬────────┬────┐ │   │
│                │  │ │☐ │用户ID   │账户名称   │总成长值 │当前成长值│过期时间 │操作│ │   │
│                │  │ ├──┼────────┼──────┼────────┼────────┼────────┼────┤ │   │
│                │  │ │☐ │user_001│GROWTH│12,500  │12,500  │永不过期 │详情│ │   │
│                │  │ │☐ │user_002│GROWTH│8,900   │8,900   │2025-01-01│详情│ │   │
│                │  │ │☐ │user_003│GROWTH│15,200  │15,200  │永不过期 │详情│ │   │
│                │  │ │☐ │user_004│GROWTH│6,800   │6,800   │2024-12-31│详情│ │   │
│                │  │ │☐ │user_005│GROWTH│22,100  │22,100  │永不过期 │详情│ │   │
│                │  │ └──┴────────┴──────┴────────┴────────┴────────┴────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 2 3 4 [>]    每页20条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.1.3 等级账户列表页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [积分账户管理] │                                                            │
│  ├ 账户列表    │  等级账户列表                                              │
│  └ 账户详情    │                                                            │
│ [系统配置]     │  ┌─────────────────────────────────────────────────────┐   │
│  ├ 账户配置    │  │ [积分账户] [成长值账户] [等级账户] [勋章账户]        │   │
│  ├ 属性配置    │  └─────────────────────────────────────────────────────┘   │
│  └ 规则配置    │                                                            │
│ [数据统计]     │  ┌─────────────────────────────────────────────────────┐   │
│ [系统监控]     │  │ 搜索条件                                            │   │
│                │  │ 用户ID: [_____________] 账户名称: [__________]      │   │
│                │  │ 等级范围: [LV1▼] ~ [LV10▼] 品牌: [全部▼]           │   │
│                │  │                              [查询] [重置] [导出]   │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 数据表格                                 共156条记录 │   │
│                │  │ ┌──┬────────┬──────┬────────┬────────┬────────┬────┐ │   │
│                │  │ │☐ │用户ID   │账户名称   │当前等级 │等级名称 │升级进度 │操作│ │   │
│                │  │ ├──┼────────┼──────┼────────┼────────┼────────┼────┤ │   │
│                │  │ │☐ │user_001│LEVEL │LV3     │白银    │85%     │详情│ │   │
│                │  │ │☐ │user_002│LEVEL │LV1     │新手    │45%     │详情│ │   │
│                │  │ │☐ │user_003│LEVEL │LV5     │铂金    │20%     │详情│ │   │
│                │  │ │☐ │user_004│LEVEL │LV2     │青铜    │90%     │详情│ │   │
│                │  │ │☐ │user_005│LEVEL │LV4     │黄金    │60%     │详情│ │   │
│                │  │ └──┴────────┴──────┴────────┴────────┴────────┴────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 2 3 4 5 6 [>] 每页20条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.1.4 勋章账户列表页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [积分账户管理] │                                                            │
│  ├ 账户列表    │  勋章账户列表                                              │
│  └ 账户详情    │                                                            │
│ [系统配置]     │  ┌─────────────────────────────────────────────────────┐   │
│  ├ 账户配置    │  │ [积分账户] [成长值账户] [等级账户] [勋章账户]        │   │
│  ├ 属性配置    │  └─────────────────────────────────────────────────────┘   │
│  └ 规则配置    │                                                            │
│ [数据统计]     │  ┌─────────────────────────────────────────────────────┐   │
│ [系统监控]     │  │ 搜索条件                                            │   │
│                │  │ 用户ID: [_____________] 勋章类型: [全部▼]           │   │
│                │  │ 获得时间: [2024-01-01] ~ [2024-01-31] 品牌: [全部▼] │   │
│                │  │                              [查询] [重置] [导出]   │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 数据表格                                 共234条记录 │   │
│                │  │ ┌──┬────────┬──────┬────────┬────────┬────────┬────┐ │   │
│                │  │ │☐ │用户ID   │勋章名称   │勋章类型 │获得时间 │勋章等级 │操作│ │   │
│                │  │ ├──┼────────┼──────┼────────┼────────┼────────┼────┤ │   │
│                │  │ │☐ │user_001│签到达人│成就类   │01-15   │🥇传说   │详情│ │   │
│                │  │ │☐ │user_001│购物狂人│消费类   │01-10   │🥈史诗   │详情│ │   │
│                │  │ │☐ │user_002│新手引导│引导类   │01-08   │🥉普通   │详情│ │   │
│                │  │ │☐ │user_003│分享专家│社交类   │01-12   │🥈史诗   │详情│ │   │
│                │  │ │☐ │user_004│VIP会员 │等级类   │01-05   │💎稀有   │详情│ │   │
│                │  │ │☐ │user_005│游戏高手│成就类   │01-14   │🥇传说   │详情│ │   │
│                │  │ └──┴────────┴──────┴────────┴────────┴────────┴────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 2 3 4 5 6 7 8 [>] 每页20条[▼] │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.1.5 功能说明

**积分账户列表功能**
- 搜索功能：
  - 用户ID：支持精确搜索和模糊搜索
  - 账户名称：下拉选择已配置的账户
  - 状态：全部/正常/冻结/异常
  - 时间范围：支持日期范围选择
- 操作功能：
  - 批量操作：批量冻结/解冻/导出
  - 点击行：跳转到账户详情页面
- 表格字段：
  - 用户ID：用户唯一标识
  - 账户名称：所属账户名称
  - 总积分：历史累计积分
  - 可用积分：当前可用积分
  - 冻结积分：被冻结的积分

**成长值账户列表功能**
- 搜索功能：
  - 用户ID：支持精确搜索和模糊搜索
  - 账户名称：下拉选择已配置的成长值账户
  - 状态：全部/正常/异常
  - 时间范围：支持日期范围选择
- 操作功能：
  - 批量操作：批量导出
  - 点击行：跳转到成长值详情页面
- 表格字段：
  - 用户ID：用户唯一标识
  - 账户名称：所属成长值账户名称
  - 总成长值：历史累计成长值（只增不减）
  - 当前成长值：当前有效成长值
  - 过期时间：成长值过期时间或永不过期

**等级账户列表功能**
- 搜索功能：
  - 用户ID：支持精确搜索和模糊搜索
  - 账户名称：下拉选择已配置的等级账户
  - 等级范围：支持等级范围筛选
  - 品牌：按品牌筛选
- 操作功能：
  - 批量操作：批量导出
  - 点击行：跳转到等级详情页面
- 表格字段：
  - 用户ID：用户唯一标识
  - 账户名称：所属等级账户名称
  - 当前等级：用户当前等级（如LV3）
  - 等级名称：等级显示名称（如白银）
  - 升级进度：距离下一等级的进度百分比

**勋章账户列表功能**
- 搜索功能：
  - 用户ID：支持精确搜索和模糊搜索
  - 勋章类型：成就类/消费类/社交类/等级类/引导类
  - 获得时间：支持日期范围选择
  - 品牌：按品牌筛选
- 操作功能：
  - 批量操作：批量导出
  - 点击行：跳转到勋章详情页面
- 表格字段：
  - 用户ID：用户唯一标识
  - 勋章名称：勋章显示名称
  - 勋章类型：勋章分类
  - 获得时间：勋章获得日期
  - 勋章等级：普通/稀有/史诗/传说等级

### 3.2 账户详情页面（多账户体系）

#### 3.2.1 积分账户详情页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [积分账户管理] │                                                            │
│  ├ 账户列表    │  积分账户详情 > user_123456                                │
│  └ 账户详情    │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 账户基本信息                              [返回列表] │   │
│                │  │ ┌─────────────┬─────────────┬─────────────────────┐ │   │
│                │  │ │ 用户ID      │ 账户名称    │ 账户状态            │ │   │
│                │  │ │ user_123456 │ MALL_APP    │ 🟢 正常             │ │   │
│                │  │ ├─────────────┼─────────────┼─────────────────────┤ │   │
│                │  │ │ 总积分      │ 可用积分    │ 冻结积分            │ │   │
│                │  │ │ 5,000       │ 4,800       │ 200                 │ │   │
│                │  │ ├─────────────┼─────────────┼─────────────────────┤ │   │
│                │  │ │ 即将过期    │ 最后更新    │ 创建时间            │ │   │
│                │  │ │ 100         │ 2024-01-15  │ 2023-06-01          │ │   │
│                │  │ └─────────────┴─────────────┴─────────────────────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 操作按钮                                            │   │
│                │  │ [发放积分] [消费积分] [冻结积分] [解冻积分] [导出明细] │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 积分明细查询                                        │   │
│                │  │ 类型: [全部▼] 来源: [全部▼] 时间: [最近30天▼]      │   │
│                │  │ 积分范围: [____] ~ [____]              [查询] [重置] │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 积分明细列表                                        │   │
│                │  │ ┌────────┬──────┬──────┬──────────┬──────────┬────┐ │   │
│                │  │ │时间     │类型   │积分   │来源      │描述       │状态│ │   │
│                │  │ ├────────┼──────┼──────┼──────────┼──────────┼────┤ │   │
│                │  │ │01-15   │收入   │+100  │每日签到   │连续签到7天│成功│ │   │
│                │  │ │01-14   │支出   │-50   │商品兑换   │兑换手机壳 │成功│ │   │
│                │  │ │01-13   │收入   │+200  │购物奖励   │消费满100元│成功│ │   │
│                │  │ │01-12   │冻结   │-100  │风险控制   │异常交易   │成功│ │   │
│                │  │ │01-11   │收入   │+50   │分享奖励   │分享商品   │成功│ │   │
│                │  │ │01-10   │支出   │-30   │商品兑换   │兑换优惠券 │成功│ │   │
│                │  │ └────────┴──────┴──────┴──────────┴──────────┴────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 2 3 4 5 [>]  每页20条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.2.2 发放积分弹窗

```
┌─────────────────────────────────────────────────────────────────┐
│ 发放积分                                                [×]     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 基本信息                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 用户ID: user_123456 (不可修改)                              │ │
│ │ 账户名称: MALL_APP (不可修改)                               │ │
│ │ 发放积分*: [_____] 分                                       │ │
│ │ 来源*: [每日签到 ▼]                                         │ │
│ │ 描述: [_________________________________]                   │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 高级设置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 有效期: [365] 天                                            │ │
│ │ 交易ID: [TXN_20240115_001_______] (可选)                    │ │
│ │ 备注: [_________________________________]                   │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 确认信息                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 当前可用积分: 4,800                                         │ │
│ │ 发放后积分: 4,800 + 100 = 4,900                            │ │
│ │ 操作时间: 2024-01-15 14:30:25                               │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│                                              [取消] [确认发放]   │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.2.3 功能说明

**账户信息展示**
- 基本信息：用户ID、账户名称、账户状态
- 积分统计：总积分、可用积分、冻结积分、即将过期积分
- 时间信息：最后更新时间、账户创建时间

**快捷操作**
- 发放积分：弹窗表单，输入积分数量、来源、描述等
- 消费积分：弹窗表单，输入消费数量、用途、描述等
- 冻结/解冻：弹窗表单，输入冻结数量、原因、期限等

**明细查询**
- 类型筛选：全部/收入/支出/冻结/解冻
- 来源筛选：签到/购物/兑换/活动等
- 时间筛选：今天/最近7天/最近30天/自定义
- 积分范围：支持积分数量范围筛选

#### 3.2.4 成长值账户详情页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [积分账户管理] │                                                            │
│  ├ 账户列表    │  [← 返回列表] 成长值账户详情 > user_123456                 │
│  └ 账户详情    │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 账户基本信息                              [返回列表] │   │
│                │  │ ┌─────────────┬─────────────┬─────────────────────┐ │   │
│                │  │ │ 用户ID      │ 账户名称    │ 账户状态            │ │   │
│                │  │ │ user_123456 │ GROWTH_MALL │ 🟢 正常             │ │   │
│                │  │ ├─────────────┼─────────────┼─────────────────────┤ │   │
│                │  │ │ 总成长值    │ 当前成长值  │ 过期时间            │ │   │
│                │  │ │ 8,500       │ 8,500       │ 永不过期            │ │   │
│                │  │ ├─────────────┼─────────────┼─────────────────────┤ │   │
│                │  │ │ 本月新增    │ 最后更新    │ 创建时间            │ │   │
│                │  │ │ 350         │ 2024-01-15  │ 2023-06-01          │ │   │
│                │  │ └─────────────┴─────────────┴─────────────────────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 操作按钮                                            │   │
│                │  │ [发放成长值] [调整成长值] [设置过期时间] [导出明细]     │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 成长值明细查询                                      │   │
│                │  │ 类型: [全部▼] 来源: [全部▼] 时间: [最近30天▼]      │   │
│                │  │ 成长值范围: [____] ~ [____]            [查询] [重置] │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 成长值明细列表                                      │   │
│                │  │ ┌────────┬──────┬──────┬──────────┬──────────┬────┐ │   │
│                │  │ │时间     │类型   │成长值 │来源      │描述       │状态│ │   │
│                │  │ ├────────┼──────┼──────┼──────────┼──────────┼────┤ │   │
│                │  │ │01-15   │增加   │+50   │每日签到   │连续签到7天│成功│ │   │
│                │  │ │01-14   │增加   │+100  │购物奖励   │消费满200元│成功│ │   │
│                │  │ │01-13   │增加   │+30   │商品评价   │五星好评   │成功│ │   │
│                │  │ │01-12   │增加   │+200  │活动奖励   │新年活动   │成功│ │   │
│                │  │ │01-11   │增加   │+25   │分享奖励   │分享商品   │成功│ │   │
│                │  │ │01-10   │调整   │-50   │系统调整   │数据修正   │成功│ │   │
│                │  │ └────────┴──────┴──────┴──────────┴──────────┴────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 2 3 4 5 [>]  每页20条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.2.5 等级账户详情页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [积分账户管理] │                                                            │
│  ├ 账户列表    │  [← 返回列表] 等级账户详情 > user_123456                   │
│  └ 账户详情    │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 账户基本信息                              [返回列表] │   │
│                │  │ ┌─────────────┬─────────────┬─────────────────────┐ │   │
│                │  │ │ 用户ID      │ 账户名称    │ 账户状态            │ │   │
│                │  │ │ user_123456 │ LEVEL_MALL  │ 🟢 正常             │ │   │
│                │  │ ├─────────────┼─────────────┼─────────────────────┤ │   │
│                │  │ │ 当前等级    │ 等级名称    │ 下一等级            │ │   │
│                │  │ │ LV3         │ 黄金会员    │ 白金会员(LV4)       │ │   │
│                │  │ ├─────────────┼─────────────┼─────────────────────┤ │   │
│                │  │ │ 升级进度    │ 升级还需    │ 等级获得时间        │ │   │
│                │  │ │ 75% ████████████░░░░      │ 500成长值   │ 2023-12-15          │ │   │
│                │  │ └─────────────┴─────────────┴─────────────────────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 等级权益                                            │   │
│                │  │ 🎁 当前等级权益:                                    │   │
│                │  │ • 购物返积分比例: 2%                                │   │
│                │  │ • 专属客服通道                                      │   │
│                │  │ • 生日礼品                                          │   │
│                │  │ • 免费配送次数: 5次/月                              │   │
│                │  │                                                     │   │
│                │  │ 🌟 下一等级权益:                                    │   │
│                │  │ • 购物返积分比例: 3%                                │   │
│                │  │ • VIP专属客服                                       │   │
│                │  │ • 生日礼品 + 优惠券                                 │   │
│                │  │ • 免费配送次数: 10次/月                             │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 操作按钮                                            │   │
│                │  │ [手动升级] [降级处理] [重置等级] [导出等级历史]       │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 等级变更历史                                        │   │
│                │  │ ┌────────┬──────────┬──────────┬──────────┬────────┐ │   │
│                │  │ │时间     │原等级     │新等级     │变更原因   │操作人  │ │   │
│                │  │ ├────────┼──────────┼──────────┼──────────┼────────┤ │   │
│                │  │ │01-15   │白银会员   │黄金会员   │自动升级   │系统    │ │   │
│                │  │ │12-20   │青铜会员   │白银会员   │自动升级   │系统    │ │   │
│                │  │ │10-15   │新手会员   │青铜会员   │自动升级   │系统    │ │   │
│                │  │ │06-01   │-         │新手会员   │账户创建   │系统    │ │   │
│                │  │ └────────┴──────────┴──────────┴──────────┴────────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 2 [>]        每页20条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.2.6 勋章账户详情页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [积分账户管理] │                                                            │
│  ├ 账户列表    │  [← 返回列表] 勋章账户详情 > user_123456                   │
│  └ 账户详情    │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 账户基本信息                              [返回列表] │   │
│                │  │ ┌─────────────┬─────────────┬─────────────────────┐ │   │
│                │  │ │ 用户ID      │ 账户名称    │ 账户状态            │ │   │
│                │  │ │ user_123456 │ BADGE_MALL  │ 🟢 正常             │ │   │
│                │  │ ├─────────────┼─────────────┼─────────────────────┤ │   │
│                │  │ │ 拥有勋章数  │ 勋章总等级  │ 本月新获得          │ │   │
│                │  │ │ 12个        │ 金级        │ 2个                 │ │   │
│                │  │ ├─────────────┼─────────────┼─────────────────────┤ │   │
│                │  │ │ 最后更新    │ 创建时间    │                     │ │   │
│                │  │ │ 2024-01-15  │ 2023-06-01  │                     │ │   │
│                │  │ └─────────────┴─────────────┴─────────────────────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 勋章展示墙                                          │   │
│                │  │ ┌─────────────────────────────────────────────────┐ │   │
│                │  │ │ 🏆 购物达人(金级)  🎯 签到王者(银级)  🌟 分享专家(铜级)│ │   │
│                │  │ │ 📝 评价高手(银级)  🎁 活动积极分子(金级)  💎 VIP会员 │ │   │
│                │  │ │ 🚀 新手引导(铜级)  🔥 热门商品收藏家(银级)  ⭐ 五星好评│ │   │
│                │  │ │ 🎊 节日活动参与者(铜级)  🏅 连续消费(金级)  📱 APP达人│ │   │
│                │  │ └─────────────────────────────────────────────────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 操作按钮                                            │   │
│                │  │ [颁发勋章] [撤销勋章] [升级勋章] [导出勋章记录]       │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 勋章获得记录                                        │   │
│                │  │ ┌────────┬──────────┬────────┬──────┬──────────────┐ │   │
│                │  │ │时间     │勋章名称   │勋章类型 │等级   │获得条件      │ │   │
│                │  │ ├────────┼──────────┼────────┼──────┼──────────────┤ │   │
│                │  │ │01-15   │购物达人   │消费类   │金级   │累计消费10000元│ │   │
│                │  │ │01-10   │签到王者   │成就类   │银级   │连续签到30天  │ │   │
│                │  │ │01-05   │分享专家   │社交类   │铜级   │分享商品10次  │ │   │
│                │  │ │12-25   │活动积极分子│成就类   │金级   │参与活动20次  │ │   │
│                │  │ │12-20   │评价高手   │引导类   │银级   │发表评价50条  │ │   │
│                │  │ │12-15   │VIP会员    │等级类   │钻石   │达到钻石会员  │ │   │
│                │  │ └────────┴──────────┴────────┴──────┴──────────────┘ │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 2 3 [>]      每页20条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 3.3 账户配置页面（多账户体系配置）

#### 3.3.1 页面布局

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [系统配置]     │                                                            │
│  ├ 账户配置    │  账户体系配置                                              │
│  ├ 属性配置    │                                                            │
│  └ 规则配置    │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ [积分账户] [成长值账户] [等级账户] [勋章账户]        │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 搜索条件                                            │   │
│                │  │ 账户代码: [_____________] 所属品牌: [_____________] │   │
│                │  │ 状态: [全部▼]                        [查询] [重置] │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 操作栏                                              │   │
│                │  │ [+ 新建配置] [批量导入] [导出配置]        共5条记录  │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 配置列表                                            │   │
│                │  │ ┌──────────┬──────────┬────────┬────────┬────────┬────────┐ │   │
│                │  │ │账户代码   │账户名称   │所属品牌 │默认有效期│最大积分 │状态    │ │   │
│                │  │ ├──────────┼──────────┼────────┼────────┼────────┼────────┤ │   │
│                │  │ │MALL_APP  │商城应用   │天猫    │365天   │100,000 │🟢启用  │ │   │
│                │  │ │GAME_APP  │游戏应用   │腾讯游戏│180天   │50,000  │🟢启用  │ │   │
│                │  │ │LIVE_APP  │直播应用   │抖音    │90天    │20,000  │🟢启用  │ │   │
│                │  │ │TEST_APP  │测试应用   │测试品牌│30天    │10,000  │🔴禁用  │ │   │
│                │  │ │VIP_APP   │会员应用   │京东    │730天   │500,000 │🟢启用  │ │   │
│                │  │ └──────────┴──────────┴────────┴────────┴────────┴────────┘ │   │
│                │  │                                                     │   │
│                │  │ 操作: [编辑] [复制] [删除] [查看详情]               │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 2 [>]        每页10条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.3.2 成长值账户配置页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [系统配置]     │                                                            │
│  ├ 账户配置    │  账户体系配置                                              │
│  ├ 属性配置    │                                                            │
│  └ 规则配置    │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ [积分账户] [成长值账户] [等级账户] [勋章账户]        │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 搜索条件                                            │   │
│                │  │ 账户代码: [_____________] 所属品牌: [_____________] │   │
│                │  │ 状态: [全部▼]                        [查询] [重置] │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 操作栏                                              │   │
│                │  │ [+ 新建配置] [批量导入] [导出配置]        共3条记录  │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 成长值账户配置列表                                  │   │
│                │  │ ┌──────────┬──────────┬────────┬────────┬────────┬────────┐ │   │
│                │  │ │账户代码   │账户名称   │所属品牌 │是否过期 │最大成长值│状态   │ │   │
│                │  │ ├──────────┼──────────┼────────┼────────┼────────┼────────┤ │   │
│                │  │ │GROWTH_MALL│商城成长值│天猫    │可配置   │1,000,000│🟢启用 │ │   │
│                │  │ │GROWTH_GAME│游戏成长值│腾讯游戏│永不过期 │500,000  │🟢启用 │ │   │
│                │  │ │GROWTH_VIP │VIP成长值 │京东    │可配置   │2,000,000│🔴禁用 │ │   │
│                │  │ └──────────┴──────────┴────────┴────────┴────────┴────────┘ │   │
│                │  │                                                     │   │
│                │  │ 操作: [编辑] [复制] [删除] [查看详情]               │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 [>]          每页10条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.3.3 等级账户配置页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [系统配置]     │                                                            │
│  ├ 账户配置    │  账户体系配置                                              │
│  ├ 属性配置    │                                                            │
│  └ 规则配置    │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ [积分账户] [成长值账户] [等级账户] [勋章账户]        │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 搜索条件                                            │   │
│                │  │ 账户代码: [_____________] 所属品牌: [_____________] │   │
│                │  │ 状态: [全部▼]                        [查询] [重置] │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 操作栏                                              │   │
│                │  │ [+ 新建配置] [批量导入] [导出配置]        共4条记录  │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 等级账户配置列表                                    │   │
│                │  │ ┌──────────┬──────────┬────────┬────────┬────────┬────────┐ │   │
│                │  │ │账户代码   │账户名称   │所属品牌 │等级数量 │升级规则 │状态    │ │   │
│                │  │ ├──────────┼──────────┼────────┼────────┼────────┼────────┤ │   │
│                │  │ │LEVEL_MALL│商城等级   │天猫    │10级    │阶梯规则 │🟢启用  │ │   │
│                │  │ │LEVEL_GAME│游戏等级   │腾讯游戏│20级    │阶梯规则 │🟢启用  │ │   │
│                │  │ │LEVEL_VIP │VIP等级    │京东    │5级     │阶梯规则 │🟢启用  │ │   │
│                │  │ │LEVEL_TEST│测试等级   │测试品牌│8级     │阶梯规则 │🔴禁用  │ │   │
│                │  │ └──────────┴──────────┴────────┴────────┴────────┴────────┘ │   │
│                │  │                                                     │   │
│                │  │ 操作: [编辑] [等级设置] [复制] [删除]               │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 [>]          每页10条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.3.4 勋章账户配置页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [系统配置]     │                                                            │
│  ├ 账户配置    │  账户体系配置                                              │
│  ├ 属性配置    │                                                            │
│  └ 规则配置    │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ [积分账户] [成长值账户] [等级账户] [勋章账户]        │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 搜索条件                                            │   │
│                │  │ 勋章代码: [_____________] 所属品牌: [_____________] │   │
│                │  │ 勋章类型: [全部▼] 状态: [全部▼]      [查询] [重置] │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 操作栏                                              │   │
│                │  │ [+ 新建勋章] [批量导入] [导出配置]        共8条记录  │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 勋章配置列表                                        │   │
│                │  │ ┌──────────┬──────────┬────────┬────────┬────────┬────────┐ │   │
│                │  │ │勋章代码   │勋章名称   │所属品牌 │勋章类型 │获得条件 │状态    │ │   │
│                │  │ ├──────────┼──────────┼────────┼────────┼────────┼────────┤ │   │
│                │  │ │BADGE_SIGN│签到达人   │天猫    │成就类   │连续签到 │🟢启用  │ │   │
│                │  │ │BADGE_BUY │购物狂人   │天猫    │消费类   │消费金额 │🟢启用  │ │   │
│                │  │ │BADGE_SHARE│分享专家  │腾讯游戏│社交类   │分享次数 │🟢启用  │ │   │
│                │  │ │BADGE_VIP │VIP会员    │京东    │等级类   │VIP等级  │🟢启用  │ │   │
│                │  │ │BADGE_NEW │新手引导   │天猫    │引导类   │完成任务 │🟢启用  │ │   │
│                │  │ │BADGE_GAME│游戏高手   │腾讯游戏│成就类   │游戏积分 │🔴禁用  │ │   │
│                │  │ └──────────┴──────────┴────────┴────────┴────────┴────────┘ │   │
│                │  │                                                     │   │
│                │  │ 操作: [编辑] [预览] [复制] [删除]                   │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 [>]          每页10条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.3.2 新建/编辑配置弹窗

```
┌─────────────────────────────────────────────────────────────────┐
│ 新建积分账户配置                                        [×]     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 账户信息                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 账户代码*: [MALL_APP_____] (创建后不可修改)                 │ │
│ │ 账户名称*: [商城应用______________________]                  │ │
│ │ 所属品牌*: [天猫__________________________]                  │ │
│ │ 账户描述:  [商城积分系统，用于用户购物奖励_____]              │ │
│ │ 状态: ○启用 ○禁用                                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 积分配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 默认有效期*: [365] 天                                       │ │
│ │ 最大积分*: [100000] 分                                      │ │
│ │ 单次发放上限*: [10000] 分                                   │ │
│ │ 单次消费上限*: [5000] 分                                    │ │
│ │ 过期提醒: ☑ 启用 提前 [7] 天提醒                           │ │
│ │ 自动清理: ☑ 启用 过期后 [30] 天自动清理                    │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 风控配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 异常检测: ☑ 启用                                           │ │
│ │ 单日发放上限: [50000] 分                                    │ │
│ │ 单日消费上限: [20000] 分                                    │ │
│ │ 频率限制: 单用户每分钟最多 [10] 次操作                      │ │
│ │ 自动冻结: ☑ 启用 触发异常时自动冻结账户                    │ │
│ │ 通知设置: ☑ 邮件通知 ☑ 短信通知                           │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 扩展配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 积分单位: [分] (如：分、点、币)                             │ │
│ │ 汇率设置: 1元 = [100] 积分                                  │ │
│ │ 等级系统: ☑ 启用 (根据积分自动升级用户等级)                │ │
│ │ 推荐奖励: ☑ 启用 推荐成功奖励 [50] 积分                    │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│                                    [测试配置] [取消] [保存]     │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.3.6 新建/编辑成长值账户配置弹窗

```
┌─────────────────────────────────────────────────────────────────┐
│ 新建成长值账户配置                                      [×]     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 账户信息                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 账户代码*: [GROWTH_MALL___] (创建后不可修改)                │ │
│ │ 账户名称*: [商城成长值______________________]                │ │
│ │ 所属品牌*: [天猫__________________________]                  │ │
│ │ 账户描述:  [用户成长值系统，只增不减_________]                │ │
│ │ 状态: ○启用 ○禁用                                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 成长值配置                                                      │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 最大成长值*: [1000000] 分                                   │ │
│ │ 单次发放上限*: [10000] 分                                   │ │
│ │ 是否过期*: ○永不过期 ○可配置过期                           │ │
│ │                                                             │ │
│ │ 过期配置 (当选择可配置过期时显示):                          │ │
│ │ 默认有效期*: [365] 天                                       │ │
│ │ 过期提醒: ☑ 启用 提前 [7] 天提醒                           │ │
│ │ 自动清理: ☑ 启用 过期后 [30] 天自动清理                    │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 风控配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 异常检测: ☑ 启用                                           │ │
│ │ 单日发放上限: [50000] 分                                    │ │
│ │ 频率限制: 单用户每分钟最多 [10] 次操作                      │ │
│ │ 自动冻结: ☑ 启用 触发异常时自动冻结账户                    │ │
│ │ 通知设置: ☑ 邮件通知 ☑ 短信通知                           │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 扩展配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 成长值单位: [分] (如：分、点、经验)                         │ │
│ │ 显示设置: ☑ 显示成长值总数 ☑ 显示成长值排行                │ │
│ │ 等级关联: ☑ 启用 (成长值影响用户等级)                      │ │
│ │ 推荐奖励: ☑ 启用 推荐成功奖励 [50] 成长值                  │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│                                    [测试配置] [取消] [保存]     │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.3.7 新建/编辑等级账户配置弹窗

```
┌─────────────────────────────────────────────────────────────────┐
│ 新建等级账户配置                                        [×]     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 账户信息                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 账户代码*: [LEVEL_MALL____] (创建后不可修改)                │ │
│ │ 账户名称*: [商城等级________________________]                │ │
│ │ 所属品牌*: [天猫__________________________]                  │ │
│ │ 账户描述:  [用户等级体系，一个用户一个等级___]                │ │
│ │ 状态: ○启用 ○禁用                                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 等级配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 等级数量*: [10] 级                                          │ │
│ │ 升级规则*: ○基于积分 ○基于成长值 ○基于消费金额             │ │
│ │ 降级规则*: ○不允许降级 ○允许降级                           │ │
│ │                                                             │ │
│ │ 阶梯规则设置:                                               │ │
│ │ ┌─────┬──────────┬──────────┬──────────┬──────────┐         │ │
│ │ │等级  │等级名称   │升级条件   │等级权益   │等级图标   │         │ │
│ │ ├─────┼──────────┼──────────┼──────────┼──────────┤         │ │
│ │ │LV1  │新手      │0         │无        │🥉       │         │ │
│ │ │LV2  │青铜      │1000      │9折优惠    │🥉       │         │ │
│ │ │LV3  │白银      │5000      │8.5折优惠  │🥈       │         │ │
│ │ │LV4  │黄金      │15000     │8折优惠    │🥇       │         │ │
│ │ │LV5  │铂金      │50000     │7.5折优惠  │💎       │         │ │
│ │ └─────┴──────────┴──────────┴──────────┴──────────┘         │ │
│ │                                                             │ │
│ │ [+ 添加等级] [批量导入] [导出模板]                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 权益配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 折扣权益: ☑ 启用 (不同等级享受不同折扣)                     │ │
│ │ 积分倍率: ☑ 启用 (高等级用户获得更多积分)                   │ │
│ │ 专属客服: ☑ 启用 (VIP等级专享客服)                          │ │
│ │ 生日特权: ☑ 启用 (生日月享受特殊优惠)                       │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 通知配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 升级通知: ☑ 启用 ☑ 站内信 ☑ 短信 ☑ 邮件                   │ │
│ │ 降级通知: ☑ 启用 ☑ 站内信 ☑ 短信 ☑ 邮件                   │ │
│ │ 权益提醒: ☑ 启用 (提醒用户使用等级权益)                     │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│                                    [测试配置] [取消] [保存]     │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.3.8 新建/编辑勋章配置弹窗

```
┌─────────────────────────────────────────────────────────────────┐
│ 新建勋章配置                                            [×]     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 勋章信息                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 勋章代码*: [BADGE_SIGN____] (创建后不可修改)                │ │
│ │ 勋章名称*: [签到达人________________________]                │ │
│ │ 所属品牌*: [天猫__________________________]                  │ │
│ │ 勋章描述:  [连续签到获得的成就勋章___________]                │ │
│ │ 勋章类型*: [成就类 ▼] (成就类/消费类/社交类/等级类/引导类)   │ │
│ │ 状态: ○启用 ○禁用                                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 获得条件                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 条件类型*: [连续签到 ▼]                                     │ │
│ │ 条件值*: [7] 天                                             │ │
│ │ 条件描述: [连续签到7天即可获得此勋章_______]                 │ │
│ │                                                             │ │
│ │ 高级条件 (可选):                                            │ │
│ │ 额外条件: ☑ 启用                                           │ │
│ │ 条件表达式: [user.totalOrderAmount >= 1000]                │ │
│ │ 条件说明: [需要累计消费满1000元]                            │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 勋章设计                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 勋章图标*: [📅] [选择图标] [上传图片]                       │ │
│ │ 勋章颜色*: [#FFD700] [🎨]                                   │ │
│ │ 勋章等级*: [普通 ▼] (普通/稀有/史诗/传说)                   │ │
│ │ 显示顺序*: [1] (数字越小越靠前)                             │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 奖励配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 获得奖励: ☑ 启用                                           │ │
│ │ 积分奖励: [100] 分                                          │ │
│ │ 成长值奖励: [50] 分                                         │ │
│ │ 优惠券奖励: ☑ 启用 [选择优惠券]                            │ │
│ │ 专属权益: [VIP客服] (可选)                                  │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 展示配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 是否可重复获得: ○仅一次 ○可重复                            │ │
│ │ 展示位置: ☑ 个人中心 ☑ 勋章墙 ☑ 排行榜                    │ │
│ │ 分享设置: ☑ 允许分享 ☑ 分享有奖励                          │ │
│ │ 过期设置: ○永不过期 ○设置过期时间 [365] 天                 │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│                                    [预览勋章] [取消] [保存]     │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.3.9 功能说明

**积分账户配置**
- 账户代码：唯一标识，创建后不可修改
- 积分配置：支持发放、消费、过期等完整生命周期管理
- 风控配置：异常检测、限额控制、自动冻结等安全措施

**成长值账户配置**
- 特点：只增不减，不能被扣减消耗
- 过期配置：可选择永不过期或可配置过期时间
- 等级关联：成长值可以影响用户等级提升
- 风控简化：主要关注发放频率和异常检测

**等级账户配置**
- 特点：一个用户在一个品牌下只有一个等级
- 阶梯规则：可配置数量的等级体系，每级有不同权益
- 升级规则：基于积分、成长值或消费金额
- 权益体系：折扣、积分倍率、专属服务等差异化权益

**勋章账户配置**
- 特点：一个用户可以拥有多个勋章
- 获得条件：灵活的条件配置，支持复合条件
- 勋章设计：图标、颜色、等级等视觉元素配置
- 奖励机制：获得勋章时的积分、成长值等奖励
- 展示配置：控制勋章的展示位置和分享规则

#### 3.3.10 积分账户功能说明

**配置管理**
- 账户代码：唯一标识，创建后不可修改
- 账户名称：显示名称，支持中文
- 状态控制：启用/禁用账户

**积分配置**
- 默认有效期：新发放积分的默认有效期
- 最大积分：单个账户最大积分限制
- 发放/消费上限：单次操作的积分数量限制
- 过期提醒：自动提醒用户积分即将过期
- 自动清理：自动清理过期积分记录

**风控配置**
- 异常检测：开启积分异常行为检测
- 日限额：每日发放和消费的总量限制
- 频率限制：防止恶意刷积分
- 自动冻结：异常时自动冻结账户
- 通知设置：异常情况的通知方式

### 3.4 属性元数据配置页面

#### 3.4.1 页面布局

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [系统配置]     │                                                            │
│  ├ 账户配置    │  属性元数据配置                                            │
│  ├ 属性配置    │                                                            │
│  └ 规则配置    │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 搜索条件                                            │   │
│                │  │ 属性代码: [_____________] 属性名称: [_____________] │   │
│                │  │ 数据类型: [全部▼] 状态: [全部▼]      [查询] [重置] │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 操作栏                                              │   │
│                │  │ [+ 新建属性] [批量导入] [导出配置]        共15条记录 │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 属性列表                                            │   │
│                │  │ ┌──────────┬──────────┬────────┬────────┬────────┐ │   │
│                │  │ │属性代码   │属性名称   │数据类型 │数据源   │状态    │ │   │
│                │  │ ├──────────┼──────────┼────────┼────────┼────────┤ │   │
│                │  │ │user.total │用户总订单 │DECIMAL │DATABASE│🟢启用  │ │   │
│                │  │ │OrderAmount│金额      │        │        │        │ │   │
│                │  │ ├──────────┼──────────┼────────┼────────┼────────┤ │   │
│                │  │ │user.order │用户订单数 │INTEGER │DATABASE│🟢启用  │ │   │
│                │  │ │Count_30d  │(30天)    │        │        │        │ │   │
│                │  │ ├──────────┼──────────┼────────┼────────┼────────┤ │   │
│                │  │ │user.level │用户等级   │STRING  │API     │🟢启用  │ │   │
│                │  │ ├──────────┼──────────┼────────┼────────┼────────┤ │   │
│                │  │ │user.signIn│连续签到   │INTEGER │DATABASE│🟢启用  │ │   │
│                │  │ │Days       │天数      │        │        │        │ │   │
│                │  │ ├──────────┼──────────┼────────┼────────┼────────┤ │   │
│                │  │ │user.vip   │VIP状态    │BOOLEAN │API     │🔴禁用  │ │   │
│                │  │ │Status     │          │        │        │        │ │   │
│                │  │ └──────────┴──────────┴────────┴────────┴────────┘ │   │
│                │  │                                                     │   │
│                │  │ 操作: [编辑] [测试] [复制] [删除]                   │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 2 3 [>]      每页10条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.4.2 新建/编辑属性弹窗

┌─────────────────────────────────────────────────────────────────┐
│ 新建属性元数据                                          [×]     │
├─────────────────────────────────────────────────────────────────┤
│ │ 属性代码*: [user.totalOrderAmount________________]          │ │
│ │            (唯一标识，支持层级结构，创建后不可修改)           │ │
│ │                                                             │ │
│ │ 属性名称*: [用户总订单金额________________________]          │ │
│ │            (显示名称，支持中文)                             │ │
│ │                                                             │ │
│ │ 属性描述:  [用户历史订单总金额_____________________]          │ │
│ │            (详细描述该属性的用途和含义)                     │ │
│ │                                                             │ │
│ │ 数据类型*: [DECIMAL ▼]                                      │ │
│ │            ├ INTEGER - 整数                                 │ │
│ │            ├ DECIMAL - 小数                                 │ │
│ │            ├ STRING - 字符串                                │ │
│ │            └ BOOLEAN - 布尔值                               │ │
│ │                                                             │ │
│ │ 默认值:    [0_______________________________]               │ │
│ │            (当属性值为空时使用的默认值)                     │ │
│ │                                                             │ │
│ │ 数据源*:   [DATABASE ▼]                                     │ │
│ │            ├ DATABASE - 数据库                              │ │
│ │            ├ API - 接口调用                                 │ │
│ │            └ CACHE - 缓存                                   │ │
│ │                                                             │ │
│ │ ┌─ 数据库配置 ─────────────────────────────────────────────┐ │ │
│ │ │ 目标表*:     [t_order_________________]                  │ │ │
│ │ │ 目标字段*:   [amount__________________]                  │ │ │
│ │ │ 聚合函数:    [SUM ▼]                                    │ │ │
│ │ │              ├ COUNT - 计数                             │ │ │
│ │ │              ├ SUM - 求和                               │ │ │
│ │ │              ├ AVG - 平均值                             │ │ │
│ │ │              ├ MAX - 最大值                             │ │ │
│ │ │              └ MIN - 最小值                             │ │ │
│ │ │                                                         │ │ │
│ │ │ 查询条件:                                               │ │ │
│ │ │ 过滤条件                                                │ │ │
│ │ │ ┌─────────────────────────────────────────────────────┐ │ │ │
│ │ │ │ 条件关系: ●并且(AND) ○或者(OR)                      │ │ │ │
│ │ │ │ ─────────────────────────────────────────────────── │ │ │ │
│ │ │ │ 订单状态: [已完成 ▼] (COMPLETED/PENDING/CANCELLED)  │ │ │ │
│ │ │ │ 【并且】是否删除: ○包含已删除 ●仅未删除             │ │ │ │
│ │ │ │ 【并且】支付状态: [全部 ▼] (PAID/UNPAID/REFUNDED)   │ │ │ │
│ │ │ │ 【并且】订单类型: [全部 ▼] (NORMAL/PROMOTION/GIFT)  │ │ │ │
│ │ │ │ 【并且】金额范围: [____] ~ [____] 元                │ │ │ │
│ │ │ │                                                     │ │ │ │
│ │ │ │ 生成的查询条件预览:                                 │ │ │ │
│ │ │ │ user_id = #{userId} AND status = 'COMPLETED'        │ │ │ │
│ │ │ │ AND deleted = false AND amount > 0                  │ │ │ │
│ │ │ │                                                     │ │ │ │
│ │ │ │ [+ 添加条件] [高级模式] (切换到SQL编辑)              │ │ │ │
│ │ │ └─────────────────────────────────────────────────────┘ │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │                                                             │ │
│ │ ┌─ API配置 ───────────────────────────────────────────────┐ │ │
│ │ │ API地址*:    [https://api.example.com/user/stats____]   │ │ │
│ │ │ 请求方法:    [GET ▼] (GET/POST)                         │ │ │
│ │ │ 请求参数:    [{"userId": "#{userId}"}_______________]    │ │ │
│ │ │              (JSON格式，支持参数占位符如#{userId})       │ │ │
│ │ │ 响应字段路径: [data.orderCount____________________]      │ │ │
│ │ │              (从API响应中提取数据的字段路径)             │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │                                                             │ │
│ │ ┌─ 缓存配置 ───────────────────────────────────────────────┐ │ │
│ │ │ 缓存键*:     [user:#{userId}:orderCount_____________]    │ │ │
│ │ │              (支持参数占位符如#{userId})                 │ │ │
│ │ │ 缓存过期时间: [3600_______] 秒 (0表示永不过期)          │ │ │
│ │ └─────────────────────────────────────────────────────────┘ │ │
│ │                                                             │ │
│ │ 刷新间隔:  [300_______] 秒 (数据刷新间隔，0表示实时刷新)    │ │
│ │                                                             │ │
│ │ 状态:      ●启用 ○禁用                                      │ │
│ │                                                             │ │
├─────────────────────────────────────────────────────────────────┤
│                    [测试查询] [取消] [保存]                     │
└─────────────────────────────────────────────────────────────────┘

**表单字段说明：**

1. **基础信息**
   - 属性代码：唯一标识，支持层级结构（如user.orderCount）
   - 属性名称：显示名称，支持中文
   - 数据类型：INTEGER/DECIMAL/STRING/BOOLEAN
   - 默认值：当属性值为空时使用的默认值

2. **数据源配置**
   - DATABASE：数据库查询
     - 目标表：查询的数据表
     - 目标字段：查询的字段
     - 聚合函数：COUNT/SUM/AVG/MAX/MIN
     - 查询条件：可视化条件构建器
   - API：接口调用
     - API地址：接口URL
     - 请求方法：GET/POST
     - 请求参数：JSON格式参数
     - 响应字段路径：提取数据的字段路径
   - CACHE：缓存读取
     - 缓存键：支持参数占位符
     - 缓存过期时间：秒为单位

3. **高级配置**
   - 刷新间隔：数据刷新频率
   - 状态：启用/禁用

┌─────────────────────────────────────────────────────────────────┐
│ 新建属性元数据                                          [×]     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 基本信息                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 属性代码*: [user.totalOrderAmount________________]          │ │
│ │ 属性名称*: [用户总订单金额________________________]          │ │
│ │ 属性描述:  [用户历史订单总金额_____________________]          │ │
│ │ 数据类型*: [DECIMAL ▼]                                      │ │
│ │ 默认值:    [0_____________________________________]          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 数据源配置                                                      │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 数据源*:   [DATABASE ▼]                                     │ │
│ │ 目标表*:   [t_order_________________________]               │ │
│ │ 聚合函数*: [SUM ▼]                                          │ │
│ │ 聚合字段*: [amount______________________]                   │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 过滤条件                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 条件关系: ●并且(AND) ○或者(OR)                              │ │
│ │ ─────────────────────────────────────────────────────────── │ │
│ │ 订单状态: [已完成 ▼] (COMPLETED/PENDING/CANCELLED)          │ │
│ │ 支付状态: [全部 ▼] (PAID/UNPAID/REFUNDED)           │ │
│ │ 订单类型: [全部 ▼] (NORMAL/PROMOTION/GIFT)          │ │
│ │ 金额范围: [____] ~ [____] 元                        │ │
│ │                                                             │ │
│ │ 生成的查询条件预览:                                         │ │
│ │ status = 'COMPLETED' AND deleted = false                    │ │
│ │ AND payment_status IN (...) AND order_type IN (...)        │ │
│ │                                                             │ │
│ │ [+ 添加条件] [高级模式] (切换到JSON编辑)                    │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 时间范围配置                                                    │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 时间类型: ○相对时间 ○绝对时间                               │ │
│ │                                                             │ │
│ │ 相对时间设置:                                               │ │
│ │ 统计最近 [30] [天 ▼] 的数据                                 │ │
│ │ 时间字段: [创建时间 ▼] (created_time/updated_time)          │ │
│ │                                                             │ │
│ │ 绝对时间设置: (当选择绝对时间时显示)                        │ │
│ │ 开始时间: [2024-01-01] 结束时间: [2024-12-31]               │ │
│ │                                                             │ │
│ │ [高级模式] (切换到JSON编辑)                                 │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 缓存配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 缓存TTL: [3600] 秒                                          │ │
│ │ 状态:    ○启用 ○禁用                                        │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│                                          [测试查询] [取消] [保存] │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.4.3 属性测试弹窗

```
┌─────────────────────────────────────────────────────────────────┐
│ 属性测试 - 用户总订单金额                               [×]     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 测试参数                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 用户ID: [user_123456_________________]                      │ │
│ │ 账户代码: [MALL_APP ▼]                                      │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 查询结果                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ✅ 查询成功                                                  │ │
│ │ SQL: SELECT SUM(amount) FROM t_order                        │ │
│ │      WHERE user_id = 'user_123456'                          │ │
│ │      AND status = 'COMPLETED'                               │ │
│ │      AND deleted = false                                    │ │
│ │      AND created_time >= '2023-12-16'                       │ │
│ │ 结果值: 15,680.50                                           │ │
│ │ 数据类型: DECIMAL                                           │ │
│ │ 执行时间: 25ms                                              │ │
│ │ 缓存状态: 已缓存 (TTL: 3600s)                               │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│                                              [重新测试] [关闭]   │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.4.4 功能说明

**属性管理**
- 属性代码：唯一标识，支持层级结构（如user.orderCount）
- 属性名称：显示名称，支持中文
- 数据类型：INTEGER/DECIMAL/STRING/BOOLEAN
- 数据源：DATABASE/API/CACHE

**查询配置**
- 目标表：数据库表名
- 聚合函数：SUM/COUNT/AVG/MAX/MIN
- 聚合字段：需要聚合的字段名
- 过滤条件：JSON格式的查询条件

**时间范围**
- 相对时间：最近N天/小时/月
- 绝对时间：指定开始和结束时间
- 时间字段：用于时间筛选的字段名

**测试功能**
- 测试查询：验证配置是否正确
- 预览结果：显示查询结果示例
- 性能测试：检查查询执行时间

### 3.5 积分规则配置页面

#### 3.5.1 页面布局

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 积分管理系统                                                    [用户头像] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [系统配置]     │                                                            │
│  ├ 账户配置    │  积分规则配置                                              │
│  ├ 属性配置    │                                                            │
│  └ 规则配置    │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 搜索条件                                            │   │
│                │  │ 规则代码: [_____________] 规则名称: [_____________] │   │
│                │  │ 账户名称: [全部▼] 状态: [全部▼]          [查询] [重置] │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 操作栏                                              │   │
│                │  │ [+ 新建规则] [批量导入] [导出配置]        共8条记录  │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 规则列表                                            │   │
│                │  │ ┌────────┬──────────┬──────┬────────┬────────┬────┐ │   │
│                │  │ │规则代码 │规则名称   │账户名称   │优先级   │状态    │操作│ │   │
│                │  │ ├────────┼──────────┼──────┼────────┼────────┼────┤ │   │
│                │  │ │SIGN_IN │每日签到   │MALL  │10      │🟢启用  │编辑│ │   │
│                │  │ ├────────┼──────────┼──────┼────────┼────────┼────┤ │   │
│                │  │ │PURCHASE│购物奖励   │MALL  │20      │🟢启用  │编辑│ │   │
│                │  │ ├────────┼──────────┼──────┼────────┼────────┼────┤ │   │
│                │  │ │NEW_USER│新用户奖励 │MALL  │30      │🟢启用  │编辑│ │   │
│                │  │ ├────────┼──────────┼──────┼────────┼────────┼────┤ │   │
│                │  │ │VIP_BONUS│VIP奖励   │MALL  │15      │🔴禁用  │编辑│ │   │
│                │  │ ├────────┼──────────┼──────┼────────┼────────┼────┤ │   │
│                │  │ │SHARE_   │分享奖励   │GAME  │5       │🟢启用  │编辑│ │   │
│                │  │ │REWARD   │          │      │        │        │    │ │   │
│                │  │ └────────┴──────────┴──────┴────────┴────────┴────┘ │   │
│                │  │                                                     │   │
│                │  │ 操作: [编辑] [测试] [复制] [删除]                   │   │
│                │  └─────────────────────────────────────────────────────┘   │
│                │                                                            │
│                │  ┌─────────────────────────────────────────────────────┐   │
│                │  │ 分页组件                                            │   │
│                │  │                    [<] 1 2 [>]        每页10条 [▼] │   │
│                │  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 3.5.2 新建/编辑规则弹窗

```
┌─────────────────────────────────────────────────────────────────┐
│ 新建积分规则                                            [×]     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 基本信息                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 规则代码*: [SIGN_IN_BONUS_________________]                 │ │
│ │ 规则名称*: [每日签到奖励______________________]              │ │
│ │ 规则描述:  [用户每日签到获得积分奖励___________]              │ │
│ │ 账户代码*: [MALL_APP ▼]                                     │ │
│ │ 优先级*:   [10_____] (数字越大优先级越高)                   │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 规则表达式                                                      │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ user.signInDays >= 1 && user.lastSignInDate != today()     │ │
│ │ ? (user.signInDays >= 7 ? 100 : 50) : 0                    │ │
│ │                                                             │ │
│ │ [表达式助手] [语法检查] [变量列表]                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 可用变量                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ • user.signInDays - 连续签到天数                            │ │
│ │ • user.totalOrderAmount - 用户总订单金额                    │ │
│ │ • user.orderCount_30d - 30天内订单数                        │ │
│ │ • user.level - 用户等级                                     │ │
│ │ • today() - 当前日期                                        │ │
│ │ • now() - 当前时间                                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 生效配置                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 生效时间: [2024-01-01 00:00:00] 过期时间: [2024-12-31]     │ │
│ │ 状态:     ○启用 ○禁用                                       │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│                                    [测试规则] [取消] [保存]     │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.5.3 规则测试弹窗

```
┌─────────────────────────────────────────────────────────────────┐
│ 规则测试 - 每日签到奖励                                 [×]     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 测试参数                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 用户ID: [user_123456_________________]                      │ │
│ │ 账户代码: [MALL_APP ▼]                                      │ │
│ │ 测试场景: [每日签到 ▼]                                      │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 变量值                                                          │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ user.signInDays: 7                                          │ │
│ │ user.lastSignInDate: 2024-01-14                             │ │
│ │ today(): 2024-01-15                                         │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ 执行结果                                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ✅ 规则执行成功                                              │ │
│ │ 表达式: user.signInDays >= 1 && user.lastSignInDate !=     │ │
│ │         today() ? (user.signInDays >= 7 ? 100 : 50) : 0    │ │
│ │ 计算过程: 7 >= 1 && '2024-01-14' != '2024-01-15'           │ │
│ │          ? (7 >= 7 ? 100 : 50) : 0                         │ │
│ │          = true && true ? 100 : 0                           │ │
│ │          = 100                                              │ │
│ │ 结果积分: 100                                               │ │
│ │ 执行时间: 15ms                                              │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│                                              [重新测试] [关闭]   │
└─────────────────────────────────────────────────────────────────┘
```

#### 3.5.4 功能说明

**规则管理**
- 规则代码：唯一标识，建议使用英文大写+下划线
- 规则名称：显示名称，支持中文
- 优先级：数字越大优先级越高，用于规则冲突时的选择
- 状态控制：启用/禁用规则

**表达式编辑**
- 支持复杂的条件判断和数学运算
- 提供变量列表和语法提示
- 实时语法检查和错误提示
- 表达式助手：常用表达式模板

**规则测试**
- 模拟真实场景测试规则
- 显示详细的计算过程
- 性能测试：检查执行时间
- 批量测试：测试多个用户场景

## 4. 交互设计规范

### 4.1 通用交互

**按钮状态**
- 主要按钮：蓝色背景，白色文字
- 次要按钮：白色背景，蓝色边框
- 危险按钮：红色背景，白色文字
- 禁用按钮：灰色背景，灰色文字

**表单验证**
- 实时验证：输入时即时检查格式
- 错误提示：红色文字，显示在字段下方
- 成功提示：绿色图标，显示在字段右侧

**数据加载**
- 骨架屏：页面首次加载时显示
- 加载动画：数据刷新时显示
- 空状态：无数据时显示友好提示

### 4.2 特殊交互

**积分操作确认**
- 发放/消费积分：二次确认弹窗
- 批量操作：显示影响的记录数量
- 危险操作：需要输入确认文字

**规则表达式编辑**
- 语法高亮：关键字、变量、函数不同颜色
- 自动补全：输入时提示可用变量和函数
- 错误标记：语法错误时红色下划线

**数据图表**
- 悬停提示：显示具体数值
- 图例交互：点击隐藏/显示数据系列
- 缩放功能：支持时间范围缩放

## 5. 响应式设计

### 5.1 断点设置
- 大屏幕：≥1200px
- 中等屏幕：992px-1199px
- 小屏幕：768px-991px
- 超小屏幕：<768px

### 5.2 适配策略

**导航适配**
- 大屏：左侧固定导航
- 中屏：可收缩导航
- 小屏：顶部导航 + 抽屉菜单

**表格适配**
- 大屏：显示所有列
- 中屏：隐藏次要列
- 小屏：卡片式布局

**表单适配**
- 大屏：多列布局
- 中屏：两列布局
- 小屏：单列布局

## 6. 总结

本原型设计涵盖了积分模块管理端的核心功能页面，包括：

1. **积分账户列表查询**：支持多条件搜索和批量操作
2. **积分账户详情查询（积分明细）**：详细的积分变动记录和快捷操作
3. **积分账户配置（可配置多个账户）**：支持多账户的积分配置管理
4. **属性元数据配置**：灵活的用户属性定义和数据源配置
5. **积分规则配置**：基于表达式的积分计算规则管理

设计遵循了现代Web应用的最佳实践，注重用户体验和可访问性，为开发团队提供了清晰的实现指导。每个功能模块都包含了完整的增删改查操作，支持测试验证，确保系统的可用性和稳定性。
