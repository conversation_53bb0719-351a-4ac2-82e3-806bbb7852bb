package com.fzucxl.open.point.param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 查询用户积分明细请求参数
 */
public class PointDetailQueryParam {
    
    /**
     * 用户唯一标识
     */
    @NotBlank(message = "用户ID不能为空")
    @Size(max = 64, message = "用户ID长度不能超过64")
    private String userId;
    
    /**
     * 账户代码
     */
    @NotBlank(message = "账户代码不能为空")
    @Size(max = 32, message = "账户代码长度不能超过32")
    private String accountCode;
    
    /**
     * 页码
     */
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum = 1;
    
    /**
     * 每页记录数
     */
    @Min(value = 1, message = "每页记录数最小为1")
    @Max(value = 100, message = "每页记录数最大为100")
    private Integer pageSize = 20;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 积分类型
     */
    private String type;
    
    /**
     * 积分来源
     */
    @Size(max = 32, message = "积分来源长度不能超过32")
    private String source;
    
    /**
     * 最小积分数
     */
    @Min(value = 1, message = "最小积分数必须大于0")
    private Long minPoint;
    
    /**
     * 最大积分数
     */
    private Long maxPoint;
    
    /**
     * 交易ID
     */
    @Size(max = 64, message = "交易ID长度不能超过64")
    private String transactionId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getMinPoint() {
        return minPoint;
    }

    public void setMinPoint(Long minPoint) {
        this.minPoint = minPoint;
    }

    public Long getMaxPoint() {
        return maxPoint;
    }

    public void setMaxPoint(Long maxPoint) {
        this.maxPoint = maxPoint;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
}