package com.fzucxl.open.point.result;

import java.util.List;

/**
 * 积分事务详情查询响应结果
 */
public class PointTransactionDetailResult {
    
    /**
     * 事务ID
     */
    private String transactionId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 账户代码
     */
    private String accountCode;
    
    /**
     * 业务单号
     */
    private String businessId;
    
    /**
     * 业务类型
     */
    private String businessType;
    
    /**
     * 事务类型
     */
    private String transactionType;
    
    /**
     * 积分变化量
     */
    private Long pointChange;
    
    /**
     * 变化前余额
     */
    private Long beforeBalance;
    
    /**
     * 变化后余额
     */
    private Long afterBalance;
    
    /**
     * 事务状态
     */
    private String status;
    
    /**
     * 事务描述
     */
    private String description;
    
    /**
     * 积分来源
     */
    private String source;
    
    /**
     * 生效时间
     */
    private String effectiveTime;
    
    /**
     * 过期时间
     */
    private String expireTime;
    
    /**
     * 处理时间
     */
    private String processedTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;
    
    /**
     * 操作人ID
     */
    private String operatorId;
    
    /**
     * 扩展数据
     */
    private String extraData;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 更新时间
     */
    private String updateTime;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 详细信息
     */
    private TransactionDetail detail;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public Long getPointChange() {
        return pointChange;
    }

    public void setPointChange(Long pointChange) {
        this.pointChange = pointChange;
    }

    public Long getBeforeBalance() {
        return beforeBalance;
    }

    public void setBeforeBalance(Long beforeBalance) {
        this.beforeBalance = beforeBalance;
    }

    public Long getAfterBalance() {
        return afterBalance;
    }

    public void setAfterBalance(Long afterBalance) {
        this.afterBalance = afterBalance;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public String getProcessedTime() {
        return processedTime;
    }

    public void setProcessedTime(String processedTime) {
        this.processedTime = processedTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }

    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getExtraData() {
        return extraData;
    }

    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public TransactionDetail getDetail() {
        return detail;
    }

    public void setDetail(TransactionDetail detail) {
        this.detail = detail;
    }

    /**
     * 事务详细信息
     */
    public static class TransactionDetail {
        
        /**
         * 规则信息
         */
        private RuleInfo ruleInfo;
        
        /**
         * 执行日志
         */
        private List<ExecutionLog> executionLog;

        public RuleInfo getRuleInfo() {
            return ruleInfo;
        }

        public void setRuleInfo(RuleInfo ruleInfo) {
            this.ruleInfo = ruleInfo;
        }

        public List<ExecutionLog> getExecutionLog() {
            return executionLog;
        }

        public void setExecutionLog(List<ExecutionLog> executionLog) {
            this.executionLog = executionLog;
        }
    }

    /**
     * 规则信息
     */
    public static class RuleInfo {
        
        /**
         * 规则ID
         */
        private Long ruleId;
        
        /**
         * 规则名称
         */
        private String ruleName;
        
        /**
         * 规则表达式
         */
        private String ruleExpression;

        public Long getRuleId() {
            return ruleId;
        }

        public void setRuleId(Long ruleId) {
            this.ruleId = ruleId;
        }

        public String getRuleName() {
            return ruleName;
        }

        public void setRuleName(String ruleName) {
            this.ruleName = ruleName;
        }

        public String getRuleExpression() {
            return ruleExpression;
        }

        public void setRuleExpression(String ruleExpression) {
            this.ruleExpression = ruleExpression;
        }
    }

    /**
     * 执行日志
     */
    public static class ExecutionLog {
        
        /**
         * 执行步骤
         */
        private String step;
        
        /**
         * 时间戳
         */
        private String timestamp;
        
        /**
         * 日志信息
         */
        private String message;
        
        /**
         * 相关数据
         */
        private Object data;

        public String getStep() {
            return step;
        }

        public void setStep(String step) {
            this.step = step;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }
    }
}