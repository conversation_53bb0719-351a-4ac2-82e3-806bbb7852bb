micronaut:
  application:
    name: fast-account
#  security:
#    authentication: bearer
#    token:
#      jwt:
#        signatures:
#          secret:
#            generator:
#              secret: ${JWT_GENERATOR_SIGNATURE_SECRET:pleaseChangeThisSecretForANewOne}
  router:
    static-resources:
      swagger:
        paths: classpath:META-INF/swagger
        mapping: /swagger/**
      swagger-ui:
        paths: classpath:META-INF/swagger/views/swagger-ui
        mapping: /swagger-ui/**
      openapi-explorer:
        paths: classpath:META-INF/swagger/views/openapi-explorer
        mapping: /openapi-explorer/**
datasources:
  default:
    driver-class-name: org.h2.Driver
    schema-generate: CREATE_DROP
    dialect: H2
