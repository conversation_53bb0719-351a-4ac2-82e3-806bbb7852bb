{
    "java.configuration.maven.userSettings": "D:\\maven\\shuyun\\settings.xml",
    "maven.executable.options": "-s D:\\maven\\shuyun\\settings.xml -Djava.home=D:\\soft\\Java\\jdk-17.0.2",
    "java.compile.nullAnalysis.mode": "automatic",
    "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx4G -Xms256m -Xlog:disable",
    "maven.view": "hierarchical",
    "java.autobuild.enabled": false,

    // 🚀 专门优化查找引用速度的关键设置
    "java.references.includeDecompiledSources": false,
    "java.references.includeAccessors": false,
    "java.referencesCodeLens.enabled": false,
    "java.implementationsCodeLens.enabled": false,

    // 🔧 减少索引范围，提升搜索速度
    "java.import.exclusions": [
        "**/target/**",
        "**/node_modules/**",
        "**/.metadata/**",
        "**/archetype-resources/**",
        "**/META-INF/maven/**"
    ],

    // 📁 文件排除优化
    "files.exclude": {
        "**/target": true,
        "**/node_modules": true,
        "**/.git": true,
        "**/.metadata": true,
        "**/.settings": true,
        "**/*.class": true
    },

    // 🔍 搜索排除优化
    "search.exclude": {
        "**/target": true,
        "**/node_modules": true,
        "**/.git": true,
        "**/logs": true,
        "**/*.log": true,
        "**/*.class": true
    },

    // ⚡ 性能优化
    "java.maxConcurrentBuilds": 1,
    "java.configuration.workspaceCacheLimit": 90,
    "java.eclipse.downloadSources": false,
    "java.maven.downloadSources": false,
    "java.progressReports.enabled": false,

    // 🎯 编辑器优化
    "editor.codeLens": false,
    "editor.hover.delay": 200,
    "editor.quickSuggestions": {
        "other": true,
        "comments": false,
        "strings": false
    },

    // 🚫 禁用不必要的功能
    "java.completion.guessMethodArguments": false,
    "java.saveActions.organizeImports": false,
    "java.configuration.updateBuildConfiguration": "disabled",
    "java.debug.settings.onBuildFailureProceed": true
}
