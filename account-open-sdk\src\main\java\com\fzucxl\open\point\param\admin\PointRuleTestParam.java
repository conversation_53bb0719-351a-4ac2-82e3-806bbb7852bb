package com.fzucxl.open.point.param.admin;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Map;

/**
 * 测试积分规则请求参数
 */
public class PointRuleTestParam {
    
    /**
     * 规则ID，测试已存在规则时使用
     */
    private Long ruleId;
    
    /**
     * 规则表达式，测试新规则时使用
     */
    @Size(max = 2000, message = "规则表达式长度不能超过2000")
    private String ruleExpression;
    
    /**
     * 测试参数
     */
    @NotNull(message = "测试参数不能为空")
    private TestParams testParams;

    /**
     * 测试参数内部类
     */
    public static class TestParams {
        
        /**
         * 用户ID
         */
        @Size(max = 64, message = "用户ID长度不能超过64")
        private String userId;
        
        /**
         * 账户代码
         */
        @NotBlank(message = "账户代码不能为空")
        @Size(max = 32, message = "账户代码长度不能超过32")
        private String accountCode;
        
        /**
         * 业务数据，根据规则类型提供不同数据
         */
        private Map<String, Object> bizData;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getAccountCode() {
            return accountCode;
        }

        public void setAccountCode(String accountCode) {
            this.accountCode = accountCode;
        }

        public Map<String, Object> getBizData() {
            return bizData;
        }

        public void setBizData(Map<String, Object> bizData) {
            this.bizData = bizData;
        }
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleExpression() {
        return ruleExpression;
    }

    public void setRuleExpression(String ruleExpression) {
        this.ruleExpression = ruleExpression;
    }

    public TestParams getTestParams() {
        return testParams;
    }

    public void setTestParams(TestParams testParams) {
        this.testParams = testParams;
    }
}
