package com.fzucxl.open.point.result.admin;

import java.time.LocalDateTime;

/**
 * 查询积分账户详情响应结果
 */
public class PointAccountDetailResult {
    
    /**
     * 账户ID
     */
    private Long id;
    
    /**
     * 账户代码
     */
    private String accountCode;
    
    /**
     * 账户名称
     */
    private String accountName;
    
    /**
     * 账户类型
     */
    private String accountType;
    
    /**
     * 品牌编码
     */
    private String brandCode;
    
    /**
     * 状态：ACTIVE-激活, INACTIVE-停用
     */
    private String status;
    
    /**
     * 账户描述
     */
    private String description;
    
    /**
     * 基础配置
     */
    private String basicConfig;
    
    /**
     * 风控配置
     */
    private String riskControlConfig;
    
    /**
     * 扩展配置
     */
    private String extensionConfig;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 版本号
     */
    private Integer version;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBasicConfig() {
        return basicConfig;
    }

    public void setBasicConfig(String basicConfig) {
        this.basicConfig = basicConfig;
    }

    public String getRiskControlConfig() {
        return riskControlConfig;
    }

    public void setRiskControlConfig(String riskControlConfig) {
        this.riskControlConfig = riskControlConfig;
    }

    public String getExtensionConfig() {
        return extensionConfig;
    }

    public void setExtensionConfig(String extensionConfig) {
        this.extensionConfig = extensionConfig;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}