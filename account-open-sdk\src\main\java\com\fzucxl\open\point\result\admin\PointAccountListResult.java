package com.fzucxl.open.point.result.admin;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 查询积分账户列表响应结果
 */
public class PointAccountListResult {
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 账户列表
     */
    private List<PointAccountInfo> list;
    
    /**
     * 积分账户信息
     */
    public static class PointAccountInfo {
        
        /**
         * 账户ID
         */
        private Long id;
        
        /**
         * 账户代码
         */
        private String accountCode;
        
        /**
         * 账户名称
         */
        private String accountName;
        
        /**
         * 账户描述
         */
        private String description;
        
        /**
         * 状态：ACTIVE-激活, INACTIVE-停用
         */
        private String status;
        
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getAccountCode() {
            return accountCode;
        }

        public void setAccountCode(String accountCode) {
            this.accountCode = accountCode;
        }

        public String getAccountName() {
            return accountName;
        }

        public void setAccountName(String accountName) {
            this.accountName = accountName;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        public void setCreateTime(LocalDateTime createTime) {
            this.createTime = createTime;
        }

        public LocalDateTime getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(LocalDateTime updateTime) {
            this.updateTime = updateTime;
        }
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<PointAccountInfo> getList() {
        return list;
    }

    public void setList(List<PointAccountInfo> list) {
        this.list = list;
    }
}