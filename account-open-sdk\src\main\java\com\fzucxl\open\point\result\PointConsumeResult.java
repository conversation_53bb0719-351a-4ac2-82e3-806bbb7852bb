package com.fzucxl.open.point.result;

import java.util.List;

/**
 * 消费积分响应结果
 */
public class PointConsumeResult {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 系统生成的交易ID
     */
    private String transactionId;
    
    /**
     * 积分明细记录ID
     */
    private String detailId;
    
    /**
     * 实际消费的积分数量
     */
    private Long point;
    
    /**
     * 消费前余额
     */
    private Long beforeBalance;
    
    /**
     * 消费后余额
     */
    private Long afterBalance;
    
    /**
     * 消费明细（按过期时间优先扣减）
     */
    private List<ConsumedDetail> consumedDetail;
    
    /**
     * 处理状态
     */
    private String status;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public Long getPoint() {
        return point;
    }

    public void setPoint(Long point) {
        this.point = point;
    }

    public Long getBeforeBalance() {
        return beforeBalance;
    }

    public void setBeforeBalance(Long beforeBalance) {
        this.beforeBalance = beforeBalance;
    }

    public Long getAfterBalance() {
        return afterBalance;
    }

    public void setAfterBalance(Long afterBalance) {
        this.afterBalance = afterBalance;
    }

    public List<ConsumedDetail> getConsumedDetail() {
        return consumedDetail;
    }

    public void setConsumedDetail(List<ConsumedDetail> consumedDetail) {
        this.consumedDetail = consumedDetail;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 消费明细
     */
    public static class ConsumedDetail {
        
        /**
         * 扣减积分数
         */
        private Long point;
        
        /**
         * 原过期时间
         */
        private String expireTime;

        public Long getPoint() {
            return point;
        }

        public void setPoint(Long point) {
            this.point = point;
        }

        public String getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(String expireTime) {
            this.expireTime = expireTime;
        }
    }
}