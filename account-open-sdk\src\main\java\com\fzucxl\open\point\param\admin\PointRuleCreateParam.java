package com.fzucxl.open.point.param.admin;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;

/**
 * 创建积分规则请求参数
 */
public class PointRuleCreateParam {
    
    /**
     * 账户代码
     */
    @NotBlank(message = "账户代码不能为空")
    @Size(max = 32, message = "账户代码长度不能超过32")
    private String accountCode;
    
    /**
     * 规则代码，唯一标识
     */
    @NotBlank(message = "规则代码不能为空")
    @Size(max = 64, message = "规则代码长度不能超过64")
    private String ruleCode;
    
    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    @Size(max = 100, message = "规则名称长度不能超过100")
    private String ruleName;
    
    /**
     * 规则类型
     */
    @NotBlank(message = "规则类型不能为空")
    @Size(max = 32, message = "规则类型长度不能超过32")
    private String ruleType;
    
    /**
     * 规则描述
     */
    @Size(max = 500, message = "规则描述长度不能超过500")
    private String description;
    
    /**
     * 规则表达式
     */
    @NotBlank(message = "规则表达式不能为空")
    @Size(max = 2000, message = "规则表达式长度不能超过2000")
    private String ruleExpression;
    
    /**
     * 优先级，数字越大优先级越高
     */
    @Min(value = 0, message = "优先级不能小于0")
    private Integer priority = 0;
    
    /**
     * 状态：ACTIVE-生效，INACTIVE-未生效
     */
    @Size(max = 20, message = "状态长度不能超过20")
    private String status = "ACTIVE";
    
    /**
     * 生效时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String effectiveTime;
    
    /**
     * 过期时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String expireTime;

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRuleExpression() {
        return ruleExpression;
    }

    public void setRuleExpression(String ruleExpression) {
        this.ruleExpression = ruleExpression;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }
}
