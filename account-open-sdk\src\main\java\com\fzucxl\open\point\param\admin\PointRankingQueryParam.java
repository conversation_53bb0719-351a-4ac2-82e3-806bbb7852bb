package com.fzucxl.open.point.param.admin;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 用户积分排行查询请求参数
 */
public class PointRankingQueryParam {
    
    /**
     * 账户代码
     */
    @NotBlank(message = "账户代码不能为空")
    @Size(max = 32, message = "账户代码长度不能超过32")
    private String accountCode;
    
    /**
     * 排行类型：BALANCE-余额排行，CONSUME-消费排行，INCOME-收入排行，默认BALANCE
     */
    @Size(max = 20, message = "排行类型长度不能超过20")
    private String type = "BALANCE";
    
    /**
     * 返回记录数，默认100，最大1000
     */
    @Min(value = 1, message = "返回记录数最小为1")
    @Max(value = 1000, message = "返回记录数最大为1000")
    private Integer limit = 100;

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }
}
