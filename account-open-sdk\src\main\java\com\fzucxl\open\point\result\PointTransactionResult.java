package com.fzucxl.open.point.result;

import java.util.List;

/**
 * 积分事务查询响应结果
 */
public class PointTransactionResult {
    
    /**
     * 总记录数
     */
    private Integer total;
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页记录数
     */
    private Integer pageSize;
    
    /**
     * 总页数
     */
    private Integer pages;
    
    /**
     * 事务记录列表
     */
    private List<PointTransactionItem> list;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public List<PointTransactionItem> getList() {
        return list;
    }

    public void setList(List<PointTransactionItem> list) {
        this.list = list;
    }

    /**
     * 积分事务项
     */
    public static class PointTransactionItem {
        
        /**
         * 事务ID
         */
        private String transactionId;
        
        /**
         * 用户ID
         */
        private String userId;
        
        /**
         * 账户代码
         */
        private String accountCode;
        
        /**
         * 业务单号
         */
        private String businessId;
        
        /**
         * 业务类型
         */
        private String businessType;
        
        /**
         * 事务类型
         */
        private String transactionType;
        
        /**
         * 积分变化量
         */
        private Long pointChange;
        
        /**
         * 变化前余额
         */
        private Long beforeBalance;
        
        /**
         * 变化后余额
         */
        private Long afterBalance;
        
        /**
         * 事务状态
         */
        private String status;
        
        /**
         * 事务描述
         */
        private String description;
        
        /**
         * 积分来源
         */
        private String source;
        
        /**
         * 生效时间
         */
        private String effectiveTime;
        
        /**
         * 过期时间
         */
        private String expireTime;
        
        /**
         * 处理时间
         */
        private String processedTime;
        
        /**
         * 错误信息
         */
        private String errorMessage;
        
        /**
         * 重试次数
         */
        private Integer retryCount;
        
        /**
         * 创建时间
         */
        private String createTime;
        
        /**
         * 更新时间
         */
        private String updateTime;

        public String getTransactionId() {
            return transactionId;
        }

        public void setTransactionId(String transactionId) {
            this.transactionId = transactionId;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getAccountCode() {
            return accountCode;
        }

        public void setAccountCode(String accountCode) {
            this.accountCode = accountCode;
        }

        public String getBusinessId() {
            return businessId;
        }

        public void setBusinessId(String businessId) {
            this.businessId = businessId;
        }

        public String getBusinessType() {
            return businessType;
        }

        public void setBusinessType(String businessType) {
            this.businessType = businessType;
        }

        public String getTransactionType() {
            return transactionType;
        }

        public void setTransactionType(String transactionType) {
            this.transactionType = transactionType;
        }

        public Long getPointChange() {
            return pointChange;
        }

        public void setPointChange(Long pointChange) {
            this.pointChange = pointChange;
        }

        public Long getBeforeBalance() {
            return beforeBalance;
        }

        public void setBeforeBalance(Long beforeBalance) {
            this.beforeBalance = beforeBalance;
        }

        public Long getAfterBalance() {
            return afterBalance;
        }

        public void setAfterBalance(Long afterBalance) {
            this.afterBalance = afterBalance;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public String getEffectiveTime() {
            return effectiveTime;
        }

        public void setEffectiveTime(String effectiveTime) {
            this.effectiveTime = effectiveTime;
        }

        public String getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(String expireTime) {
            this.expireTime = expireTime;
        }

        public String getProcessedTime() {
            return processedTime;
        }

        public void setProcessedTime(String processedTime) {
            this.processedTime = processedTime;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public Integer getRetryCount() {
            return retryCount;
        }

        public void setRetryCount(Integer retryCount) {
            this.retryCount = retryCount;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }
    }
}