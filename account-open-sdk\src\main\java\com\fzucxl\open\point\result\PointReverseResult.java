package com.fzucxl.open.point.result;

/**
 * 交易撤销响应结果
 */
public class PointReverseResult {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 撤销交易ID
     */
    private String reverseTransactionId;
    
    /**
     * 原交易信息
     */
    private OriginalTransaction originalTransaction;
    
    /**
     * 实际撤销的积分数量
     */
    private Long reversePoint;
    
    /**
     * 撤销前余额
     */
    private Long beforeBalance;
    
    /**
     * 撤销后余额
     */
    private Long afterBalance;
    
    /**
     * 处理状态
     */
    private String status;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getReverseTransactionId() {
        return reverseTransactionId;
    }

    public void setReverseTransactionId(String reverseTransactionId) {
        this.reverseTransactionId = reverseTransactionId;
    }

    public OriginalTransaction getOriginalTransaction() {
        return originalTransaction;
    }

    public void setOriginalTransaction(OriginalTransaction originalTransaction) {
        this.originalTransaction = originalTransaction;
    }

    public Long getReversePoint() {
        return reversePoint;
    }

    public void setReversePoint(Long reversePoint) {
        this.reversePoint = reversePoint;
    }

    public Long getBeforeBalance() {
        return beforeBalance;
    }

    public void setBeforeBalance(Long beforeBalance) {
        this.beforeBalance = beforeBalance;
    }

    public Long getAfterBalance() {
        return afterBalance;
    }

    public void setAfterBalance(Long afterBalance) {
        this.afterBalance = afterBalance;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 原交易信息
     */
    public static class OriginalTransaction {
        
        /**
         * 原交易ID
         */
        private String transactionId;
        
        /**
         * 原交易积分数量
         */
        private Long point;
        
        /**
         * 原交易类型
         */
        private String type;
        
        /**
         * 原交易时间
         */
        private String createTime;

        public String getTransactionId() {
            return transactionId;
        }

        public void setTransactionId(String transactionId) {
            this.transactionId = transactionId;
        }

        public Long getPoint() {
            return point;
        }

        public void setPoint(Long point) {
            this.point = point;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }
    }
}