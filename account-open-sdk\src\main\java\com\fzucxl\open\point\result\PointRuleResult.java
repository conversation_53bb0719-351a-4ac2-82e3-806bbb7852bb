package com.fzucxl.open.point.result;

import java.util.List;

/**
 * 获取积分规则响应结果
 */
public class PointRuleResult {
    
    /**
     * 规则列表
     */
    private List<PointRuleItem> list;

    public List<PointRuleItem> getList() {
        return list;
    }

    public void setList(List<PointRuleItem> list) {
        this.list = list;
    }

    /**
     * 积分规则项
     */
    public static class PointRuleItem {
        
        /**
         * 规则代码
         */
        private String ruleCode;
        
        /**
         * 规则名称
         */
        private String ruleName;
        
        /**
         * 规则类型
         */
        private String ruleType;
        
        /**
         * 规则描述
         */
        private String description;
        
        /**
         * 积分值
         */
        private Integer point;
        
        /**
         * 状态
         */
        private String status;
        
        /**
         * 生效时间
         */
        private String effectiveTime;
        
        /**
         * 过期时间
         */
        private String expireTime;

        public String getRuleCode() {
            return ruleCode;
        }

        public void setRuleCode(String ruleCode) {
            this.ruleCode = ruleCode;
        }

        public String getRuleName() {
            return ruleName;
        }

        public void setRuleName(String ruleName) {
            this.ruleName = ruleName;
        }

        public String getRuleType() {
            return ruleType;
        }

        public void setRuleType(String ruleType) {
            this.ruleType = ruleType;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Integer getPoint() {
            return point;
        }

        public void setPoint(Integer point) {
            this.point = point;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getEffectiveTime() {
            return effectiveTime;
        }

        public void setEffectiveTime(String effectiveTime) {
            this.effectiveTime = effectiveTime;
        }

        public String getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(String expireTime) {
            this.expireTime = expireTime;
        }
    }
}