package com.fzucxl.open.point.result;

import java.util.List;

/**
 * 查询用户积分明细响应结果
 */
public class PointDetailResult {
    
    /**
     * 总记录数
     */
    private Integer total;
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页记录数
     */
    private Integer pageSize;
    
    /**
     * 总页数
     */
    private Integer pages;
    
    /**
     * 积分明细列表
     */
    private List<PointDetailItem> list;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public List<PointDetailItem> getList() {
        return list;
    }

    public void setList(List<PointDetailItem> list) {
        this.list = list;
    }

    /**
     * 积分明细项
     */
    public static class PointDetailItem {
        
        /**
         * 记录唯一标识
         */
        private String id;
        
        /**
         * 用户ID
         */
        private String userId;
        
        /**
         * 账户代码
         */
        private String accountCode;
        
        /**
         * 积分数量（正数为收入，负数为支出）
         */
        private Long point;
        
        /**
         * 类型
         */
        private String type;
        
        /**
         * 积分来源/用途
         */
        private String source;
        
        /**
         * 来源名称
         */
        private String sourceName;
        
        /**
         * 详细描述
         */
        private String description;
        
        /**
         * 业务单号
         */
        private String businessId;
        
        /**
         * 交易ID
         */
        private String transactionId;
        
        /**
         * 状态
         */
        private String status;
        
        /**
         * 创建时间
         */
        private String createTime;
        
        /**
         * 过期时间
         */
        private String expireTime;
        
        /**
         * 备注信息
         */
        private String remark;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getAccountCode() {
            return accountCode;
        }

        public void setAccountCode(String accountCode) {
            this.accountCode = accountCode;
        }

        public Long getPoint() {
            return point;
        }

        public void setPoint(Long point) {
            this.point = point;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public String getSourceName() {
            return sourceName;
        }

        public void setSourceName(String sourceName) {
            this.sourceName = sourceName;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getBusinessId() {
            return businessId;
        }

        public void setBusinessId(String businessId) {
            this.businessId = businessId;
        }

        public String getTransactionId() {
            return transactionId;
        }

        public void setTransactionId(String transactionId) {
            this.transactionId = transactionId;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(String expireTime) {
            this.expireTime = expireTime;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
}