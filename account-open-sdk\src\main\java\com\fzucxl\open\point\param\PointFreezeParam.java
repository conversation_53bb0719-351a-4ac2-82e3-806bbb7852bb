package com.fzucxl.open.point.param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 冻结积分请求参数
 */
public class PointFreezeParam {
    
    /**
     * 用户唯一标识
     */
    @NotBlank(message = "用户ID不能为空")
    @Size(max = 64, message = "用户ID长度不能超过64")
    private String userId;
    
    /**
     * 账户代码
     */
    @NotBlank(message = "账户代码不能为空")
    @Size(max = 32, message = "账户代码长度不能超过32")
    private String accountCode;
    
    /**
     * 冻结积分数量
     */
    @NotNull(message = "冻结积分数量不能为空")
    @Min(value = 1, message = "冻结积分数量最小为1")
    @Max(value = 100000, message = "冻结积分数量最大为100000")
    private Long point;
    
    /**
     * 冻结类型
     */
    private String freezeType = "RISK_CONTROL";
    
    /**
     * 冻结原因
     */
    @NotBlank(message = "冻结原因不能为空")
    @Size(max = 100, message = "冻结原因长度不能超过100")
    private String reason;
    
    /**
     * 冻结过期时间
     */
    private String expireTime;
    
    /**
     * 是否自动解冻
     */
    private Boolean autoUnfreeze = false;
    
    /**
     * 业务单号
     */
    @NotBlank(message = "业务单号不能为空")
    @Size(max = 64, message = "业务单号长度不能超过64")
    private String businessId;
    
    /**
     * 交易ID
     */
    @Size(max = 64, message = "交易ID长度不能超过64")
    private String transactionId;
    
    /**
     * 备注信息
     */
    @Size(max = 100, message = "备注信息长度不能超过100")
    private String remark;
    
    /**
     * 回调通知地址
     */
    private String notifyUrl;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public Long getPoint() {
        return point;
    }

    public void setPoint(Long point) {
        this.point = point;
    }

    public String getFreezeType() {
        return freezeType;
    }

    public void setFreezeType(String freezeType) {
        this.freezeType = freezeType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public Boolean getAutoUnfreeze() {
        return autoUnfreeze;
    }

    public void setAutoUnfreeze(Boolean autoUnfreeze) {
        this.autoUnfreeze = autoUnfreeze;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }
}