package com.fzucxl.open.point.result;

import java.util.List;

/**
 * 计算积分响应结果
 */
public class PointCalculateResult {
    
    /**
     * 计算得出的积分值
     */
    private Integer point;
    
    /**
     * 适用的规则列表
     */
    private List<AppliedRule> rule;

    public Integer getPoint() {
        return point;
    }

    public void setPoint(Integer point) {
        this.point = point;
    }

    public List<AppliedRule> getRule() {
        return rule;
    }

    public void setRule(List<AppliedRule> rule) {
        this.rule = rule;
    }

    /**
     * 适用的规则
     */
    public static class AppliedRule {
        
        /**
         * 规则代码
         */
        private String ruleCode;
        
        /**
         * 规则名称
         */
        private String ruleName;
        
        /**
         * 该规则贡献的积分值
         */
        private Integer point;

        public String getRuleCode() {
            return ruleCode;
        }

        public void setRuleCode(String ruleCode) {
            this.ruleCode = ruleCode;
        }

        public String getRuleName() {
            return ruleName;
        }

        public void setRuleName(String ruleName) {
            this.ruleName = ruleName;
        }

        public Integer getPoint() {
            return point;
        }

        public void setPoint(Integer point) {
            this.point = point;
        }
    }
}