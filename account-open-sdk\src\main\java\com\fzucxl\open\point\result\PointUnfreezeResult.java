package com.fzucxl.open.point.result;

/**
 * 解冻积分响应结果
 */
public class PointUnfreezeResult {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 系统生成的交易ID
     */
    private String transactionId;
    
    /**
     * 实际解冻的积分数量
     */
    private Long point;
    
    /**
     * 解冻前可用余额
     */
    private Long beforeAvailable;
    
    /**
     * 解冻后可用余额
     */
    private Long afterAvailable;
    
    /**
     * 解冻前冻结余额
     */
    private Long beforeFrozen;
    
    /**
     * 解冻后冻结余额
     */
    private Long afterFrozen;
    
    /**
     * 该冻结记录剩余冻结积分
     */
    private Long remainingFrozen;
    
    /**
     * 处理状态
     */
    private String status;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Long getPoint() {
        return point;
    }

    public void setPoint(Long point) {
        this.point = point;
    }

    public Long getBeforeAvailable() {
        return beforeAvailable;
    }

    public void setBeforeAvailable(Long beforeAvailable) {
        this.beforeAvailable = beforeAvailable;
    }

    public Long getAfterAvailable() {
        return afterAvailable;
    }

    public void setAfterAvailable(Long afterAvailable) {
        this.afterAvailable = afterAvailable;
    }

    public Long getBeforeFrozen() {
        return beforeFrozen;
    }

    public void setBeforeFrozen(Long beforeFrozen) {
        this.beforeFrozen = beforeFrozen;
    }

    public Long getAfterFrozen() {
        return afterFrozen;
    }

    public void setAfterFrozen(Long afterFrozen) {
        this.afterFrozen = afterFrozen;
    }

    public Long getRemainingFrozen() {
        return remainingFrozen;
    }

    public void setRemainingFrozen(Long remainingFrozen) {
        this.remainingFrozen = remainingFrozen;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}