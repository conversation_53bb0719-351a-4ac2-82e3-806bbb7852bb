package com.fzucxl.open.point.param.admin;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 查询积分账户列表请求参数
 */
public class PointAccountListQueryParam {
    
    /**
     * 品牌代码
     */
    @NotBlank(message = "品牌代码不能为空")
    @Size(max = 64, message = "品牌代码长度不能超过64")
    private String brandCode;
    
    /**
     * 账户代码，支持模糊匹配
     */
    @Size(max = 100, message = "账户代码长度不能超过100")
    private String accountCode;
    
    /**
     * 账户状态：ACTIVE/INACTIVE
     */
    @Size(max = 20, message = "账户状态长度不能超过20")
    private String status;
    
    /**
     * 页码，默认1
     */
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum = 1;
    
    /**
     * 每页记录数，默认20
     */
    @Min(value = 1, message = "每页记录数最小为1")
    @Max(value = 100, message = "每页记录数最大为100")
    private Integer pageSize = 20;

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
